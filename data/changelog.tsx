import { Badge, type BadgeProps } from '@/components/ui/badge'

const variantMap: {
  [key: string]: BadgeProps
} = {
  公告: { tint: 'rose' },
  配置器: { tint: 'yellow' },
  全局: { tint: 'indigo' },
  控制台: { tint: 'teal' },
  OBS: { tint: 'purple' },
  LEF: { tint: 'sky' },
  开发者: { tint: 'orange' },
  同步器: { tint: 'pink' },
  模版: { tint: 'fuchsia' },
  其他: { tint: 'default' },
}

export type ChangelogItemProps = {
  id: number
  timestamp: string
  /**
   * 当前 changeset 尚未完成开发是标记为 `true`
   */
  pending?: boolean
  content: string | React.ReactNode
}

function TypeBadge({ type }: { type: string }) {
  return (
    <Badge className='w-[65px]' tint={variantMap[type]?.tint} title={String(type)}>
      {type}
    </Badge>
  )
}

function ChangeItem({ type, content }: { type: string; content: React.ReactNode }) {
  return (
    <div className='flex gap-2'>
      <div className='shrink-0'>
        <TypeBadge type={type} />
      </div>
      <div className='space-y-2'>{content}</div>
    </div>
  )
}

// prettier-ignore
export const ChangelogItems: ChangelogItemProps[] = [
  {
    id: 132,
    timestamp: 'Jul 27, 2025, 3:59:53 PM GMT+8',
    pending: false,
    content: <>
      <div className='grid mb-4 gap-2'>
        <ChangeItem type='控制台' content={<>现在云端事件同步支持显示近期弹幕事件（效果参考首次打开直播间）。开发者需将 LEF 更新至最新版，并配置 Redis 即可使用该功能</>} />
        <ChangeItem type='LEF' content={<>支持近期弹幕事件的临时存储</>} />
        <ChangeItem type='LEF' content={<>数据库读取性能优化，Redis 作为可选依赖不再提供缓存支援，而是用于存储近期弹幕事件</>} />
      </div>
    </>
  },
  {
    id: 131,
    timestamp: 'Jul 26, 2025, 12:52:31 AM GMT+8',
    pending: false,
    content: <>
      <div className='grid mb-4 gap-2'>
        <ChangeItem type='控制台' content={<>回滚 #113 中对云端事件的载入逻辑的优化。开发者注：由于该优化存在逻辑问题，在某些条件下会导致事件无法被同步，现回滚</>} />
      </div>
    </>
  },
  {
    id: 130,
    timestamp: 'Jul 25, 2025, 5:30:28 PM GMT+8',
    pending: false,
    content: <>
      <div className='grid mb-4 gap-2'>
        <ChangeItem type='控制台' content={<>略微优化标记为已读/未读的数据库写入速度</>} />
        <ChangeItem type='控制台' content={<>云端事件的自动获取间隔由每 5 分钟上调至每 20 分钟。开发者注：根据观测，之前的更新间隔过于激进，加上用户网络问题，可能会导致控制台频繁处于处理事件状态。现延长自动获取间隔。窗口激活时的自动获取逻辑保持不变</>} />
        <ChangeItem type='控制台' content={<>优化数据库处理事件时的提示文案（视觉上处理速度更快）</>} />
        <ChangeItem type='控制台' content={<>清空数据库支持选择更长的时间范围</>} />
      </div>
    </>
  },
  {
    id: 129,
    timestamp: 'Jul 24, 2025, 4:24:12 PM GMT+8',
    pending: false,
    content: <>
      <div className='grid mb-4 gap-2'>
        <ChangeItem type='全局' content={<>优化礼物特效调试逻辑，现在主播在自己的直播间可以通过输入 <code>g:gift_id</code> 来测试礼物特效，无论该功能是否开启</>} />
        <ChangeItem type='开发者' content={<>重构事件的 CSS 实现，现在事件不再基于 CSS Modules 提供 <code>template</code> 层叠层，而是复用模版自身的 CSS 类。开发者注：理论上，目前的模版不会有任何兼容问题（Storybook 0 差异），如不放心可自行二次确认</>} />
        <ChangeItem type='开发者' content={<>所有事件均不再提供 <code>data-read</code> 属性，请通过 CSS 类选取</>} />
        <ChangeItem type='开发者' content={<>礼物事件不再提供 <code>data-gift-rank</code> 属性，请通过 CSS 类选取</>} />
        <ChangeItem type='开发者' content={<>礼物事件不再提供 <code>data-gift-id</code> 属性，请通过 CSS 类选取</>} />
        <ChangeItem type='开发者' content={<>礼物事件不再提供 <code>data-gift-price</code> 属性</>} />
        <ChangeItem type='开发者' content={<>礼物事件不再提供 <code>data-gift-type</code> 属性，并且这个属性之前也是一直错误的</>} />
        <ChangeItem type='开发者' content={<>礼物事件不再提供 <code>data-gift-amount</code> 属性</>} />
        <ChangeItem type='开发者' content={<>红包开始事件不再提供 <code>data-red-envelope-start-price</code> 属性</>} />
        <ChangeItem type='开发者' content={<>红包开始事件不再提供 <code>data-red-envelope-start-rank</code> 属性</>} />
        <ChangeItem type='开发者' content={<>醒目留言事件不再提供 <code>data-superchat-amount</code> 属性</>} />
        <ChangeItem type='开发者' content={<>醒目留言事件不再提供 <code>data-superchat-price</code> 属性</>} />
        <ChangeItem type='开发者' content={<>醒目留言事件不再提供 <code>data-superchat-id</code> 属性，请通过 CSS 类选取</>} />
        <ChangeItem type='开发者' content={<>醒目留言事件不再提供 <code>data-superchat-rank</code> 属性，请通过 CSS 类选取</>} />
        <ChangeItem type='开发者' content={<>大航海事件不再提供 <code>data-toast-price</code> 属性</>} />
        <ChangeItem type='开发者' content={<>大航海事件不再提供 <code>data-toast-amount</code> 属性</>} />
        <ChangeItem type='开发者' content={<>大航海事件不再提供 <code>data-toast-type</code> 属性，请通过 CSS 类选取</>} />
        <ChangeItem type='开发者' content={<>大航海事件不再提供 <code>data-frame-rank</code> 属性，请通过 CSS 类选取</>} />
        <ChangeItem type='开发者' content={<>大航海事件不再提供 <code>data-badge-rank</code> 属性，请通过 CSS 类选取</>} />
        <ChangeItem type='开发者' content={<>修复红包结果事件样式多层嵌套的问题</>} />
        <ChangeItem type='其他' content='其他细节调整' />
      </div>
    </>
  },
  {
    id: 128,
    timestamp: 'Jul 23, 2025, 1:22:43 AM GMT+8',
    pending: false,
    content: <>
      <div className='grid mb-4 gap-2'>
        <ChangeItem type='公告' content={<>禁言事件的显示受限：禁言事件现在仅会在同步登录账号为对应主播或房管时方可显示。此限制为哔哩哔哩官方限制，其他条件下（匿名、他人的同步登录状态）将不会再显示</>} />
        <ChangeItem type='LEF' content={<>优化对鉴权的解析，建议更新</>} />
        <ChangeItem type='LEF' content={<>支持解析更多事件</>} />
      </div>
    </>
  },
  {
    id: 127,
    timestamp: 'Jul 18, 2025, 3:55:20 PM GMT+8',
    pending: false,
    content: <>
      <div className='grid mb-4 gap-2'>
        <ChangeItem type='控制台' content={<>减少高能榜对外部头像服务的依赖</>} />
        <ChangeItem type='控制台' content={<>修复内容翻译、TTS 文本转语音翻译不工作的问题</>} />
        <ChangeItem type='控制台' content={<>修复按范围清空数据库时，时间范围计算有偏差的问题</>} />
        <ChangeItem type='控制台' content={<>现在「显示直播间基本信息」和「显示高能榜用户」不再强制关联，可独立关闭</>} />
        <ChangeItem type='其他' content='其他细节调整' />
      </div>
    </>
  },
  {
    id: 126,
    timestamp: 'Jul 15, 2025, 7:21:35 PM GMT+8',
    pending: false,
    content: <>
      <div className='grid mb-4 gap-2'>
        <ChangeItem type='控制台' content={<>优化移动端适配</>} />
        <ChangeItem type='其他' content='其他细节调整' />
      </div>
    </>
  },
  {
    id: 125,
    timestamp: 'Jul 5, 2025, 0:58:15 AM GMT+8',
    pending: false,
    content: <>
      <div className='grid mb-4 gap-2'>
        <ChangeItem type='全局' content={<>为了跟进哔哩哔哩今后的降本增效，本站采用了独立自主研发™的 Schemaless Protocol Buffers Inspecting System®（aka. SPBIS）对新格式进行解析，如新支持的事件遇到任何与之前不一样的行为，欢迎通过 Discord 反馈</>} />
        <ChangeItem type='全局' content={<>支持新版互动事件（进入直播间、关注、分享）</>} />
        <ChangeItem type='全局' content={<>优化新版礼物事件对盲盒礼物的解析</>} />
        <ChangeItem type='控制台' content={<>支持新版高能榜事件</>} />
        <ChangeItem type='LEF' content={<>支持解析新增的事件，建议升级</>} />
      </div>
    </>
  },
  {
    id: 124,
    timestamp: 'Jul 4, 2025, 7:39:51 PM GMT+8',
    pending: false,
    content: <>
      <div className='grid mb-4 gap-2'>
        <ChangeItem type='LEF' content={<>框架由 Hono 迁移至 Elysia，优化 Bridge Mode 下 WebSocket 的性能，带来更低的 CPU 占用</>} />
        <ChangeItem type='LEF' content={<>不再支持 Sentry，改为更加通用的 OpenTelemetry 集成。现有的 Sentry 集成将不再可用，如需迁移请查看 <a href="https://subspace.institute/docs/laplace-chat/event-fetcher#opentelemetry" target="_blank">亚空间研究所</a></>} />
        <ChangeItem type='LEF' content={<>现在不再限制 CORS 请求。开发者注：此改动可以允许开发者建立跨域请求，能够更容易地创建第三方 web 应用。如果您的 LEF 节点不想被跨域请求，请为其添加认证</>} />
        <ChangeItem type='LEF' content={<>集成了 OpenAPI 文档，您可以通过 <code>/openapi</code> 获取</>} />
      </div>
    </>
  },
  {
    id: 123,
    timestamp: 'Jul 1, 2025, 11:52:17 PM GMT+8',
    pending: false,
    content: <>
      <div className='grid mb-4 gap-2'>
        <ChangeItem type='控制台' content={<>优化控制台基本信息更新样式</>} />
        <ChangeItem type='控制台' content={<>事件导出兼容更多格式</>} />
      </div>
    </>
  },
  {
    id: 122,
    timestamp: 'Jun 26, 2025, 1:35:10 AM GMT+8',
    pending: false,
    content: <>
      <div className='grid mb-4 gap-2'>
        <ChangeItem type='OBS' content={<>
          <div>
            移除实验特性「n秒后自动隐藏弹幕」。开发者注：该功能于 #42 引入，但根据观测效果不达预期，多数主播会对此选项产生困扰，因此移除该功能。模版作者可继续通过纯 CSS 实现类似效果，以下是一个简单的示例：
          </div>
          <div>
            <pre className='whitespace-pre-wrap text-sm'>{`.event {
  animation: hide .5s ease-out 5s forwards;
}

@keyframes hide {
  to {
    opacity: 0;
    height: 0;
    padding-top: 0;
    padding-bottom: 0;
    margin-top: 0;
    margin-bottom: 0;
  }
}`}
            </pre>
          </div>
        </>} />
        <ChangeItem type='其他' content='其他细节调整' />
      </div>
    </>
  },
  {
    id: 121,
    timestamp: 'Jun 18, 2025, 12:08:43 PM PDT',
    pending: false,
    content: <>
      <div className='grid mb-4 gap-2'>
        <ChangeItem type='全局' content={<>
          <div>
            优化礼物特效展示，现在礼物特效在浅色背景下看上去不会再有偏脏的黑色渲染
          </div>
          <picture className='block'>
            <img src='https://d3ksmgl05f2csh.cloudfront.net/assets/campaigns/gift-effect-update.png' className='max-w-full rounded-md' width='410' alt='礼物特效更新' loading='lazy' />
          </picture>
        </>} />
        <ChangeItem type='全局' content={<>优化红包事件中的奖励数量视觉呈现</>} />
        <ChangeItem type='配置器' content={<>编辑器不再支持元素提取功能。开发者注：熟悉 HTML 的开发者不会用这个功能，不熟悉的用了也用不明白</>} />
        <ChangeItem type='配置器' content={<>现在高级 CSS 编辑器的 AI 代码补全功能不再强制要求浏览器同步登录。无同步登录时将使用 Llama 小模型；有同步登录时将继续使用效果更好的 Codestral 模型</>} />
      </div>
    </>
  },
  {
    id: 120,
    timestamp: 'Jun 15, 2025, 6:24:29 AM PDT',
    pending: false,
    content: <>
      <div className='grid mb-4 gap-2'>
        <ChangeItem type='配置器' content={<>现在高级 CSS 编辑器支持 AI 代码补全功能。开启浏览器同步登录即可免费使用</>} />
        <ChangeItem type='LEF' content={<>修复 laplace-event-fetcher 的 Bridge Mode 没有广播天选结果事件的问题</>} />
      </div>
    </>
  },
  {
    id: 119,
    timestamp: 'Jun 14, 2025, 12:39:16 PM PDT',
    pending: false,
    content: <>
      <div className='grid mb-4 gap-2'>
        <ChangeItem type='控制台' content={<>修复云端事件同步设置界面进行测试时无法传入认证密钥的问题</>} />
        <ChangeItem type='其他' content={<>部分用户有在浏览器后台运行控制台的习惯，即在浏览器中，控制台的标签页长期处于非激活状态。由于浏览器的限制，长期未激活的标签会被浏览器的节能模式回收，导致无法获取完整事件。为解决此方法，您可以在浏览器中关闭 <code>chat.laplace.live</code> 的节能模式，或自行部署 laplace-event-fetcher 以激活云端获取</>} />
      </div>
    </>
  },
  {
    id: 118,
    timestamp: 'Jun 13, 2025, 11:00:23 AM PDT',
    pending: false,
    content: <>
      <div className='grid mb-4 gap-2'>
        <ChangeItem type='模版' content={<>修复简约气泡排行榜标记显示错误的问题</>} />
        <ChangeItem type='配置器' content={<>修复编辑器在某些老版本浏览器中无法载入的问题</>} />
        <ChangeItem type='其他' content='其他细节调整' />
      </div>
    </>
  },
  {
    id: 117,
    timestamp: 'Jun 7, 2025, 1:02:49 AM PDT',
    pending: false,
    content: <>
      <div className='grid mb-4 gap-2'>
        <ChangeItem type='LEF' content={<>laplace-event-fetcher 现在支持 Bridge Mode。在开启 Bridge Mode 后，LEF 将暴露一个 WebSocket 连接，实时广播所有收到的 <code>LaplaceEvent[]</code> 事件。开发者注：该功能可用来代替现有的 LAPLACE Event Bridge Server，而无需保持控制台一直打开，提供了更加稳定的事件连接方式。推荐高级用户使用。详细文档请参考 <a href="https://subspace.institute/docs/laplace-chat/event-bridge#event-fetcher-bridge-mode" target="_blank">亚空间研究所</a></>} />
        <ChangeItem type='开发者' content={<>现在可以通过 <code>reduced-motion</code> 或 <code>can-motion</code> 条件类名来控制事件的动效，当事件速率大于特定值时，事件列表将应用全局的 <code>reduced-motion</code> 类名，否则应用 <code>can-motion</code> 类名。查看简约气泡（复刻）模版查看用例。控制台模式下为增强可读性，则总是应用 <code>reduced-motion</code> 类名</>} />
      </div>
    </>
  },
  {
    id: 116,
    timestamp: 'Jun 2, 2025, 1:23:59 PM PDT',
    pending: false,
    content: <>
      <div className='grid mb-4 gap-2'>
        <ChangeItem type='控制台' content={<>现在可以实时的更新粉丝团人数</>} />
        <ChangeItem type='控制台' content={<>直播间基本信息弹出菜单中，不再支持显示直播间画面关键帧。开发者注：由于 #115 更新降低了对外部 API 的依赖，该功能现在无法像之前那样实时的获取画面关键帧，因此不再具有存在的意义</>} />
      </div>
    </>
  },
  {
    id: 115,
    timestamp: 'May 31, 2025, 1:05:58 PM PDT',
    pending: false,
    content: <>
      <div className='grid mb-4 gap-2'>
        <ChangeItem type='全局' content={<>降低对外部 API 的依赖，现在可以更实时、准确地计算千舰长、万舰状态</>} />
        <ChangeItem type='OBS' content={<>优化页面的 FCP 首次内容绘制时间（翻译：视觉上感觉载入更快一些）</>} />
        <ChangeItem type='控制台' content={<>现在在控制台中修改同步密钥不会再强制刷新页面</>} />
        <ChangeItem type='控制台' content={<>修复控制台在某些特殊情况下载入白屏的问题</>} />
        <ChangeItem type='控制台' content={<>OBS 集成现在支持显示和编辑推流服务设置</>} />
        <ChangeItem type='控制台' content={<>OBS 集成现在支持控制视频录制</>} />
        <ChangeItem type='控制台' content={<>OBS 集成现在可以更准确地从 OBS 获取推流状态，不会再出现「停止推流失败」的提示</>} />
        <ChangeItem type='控制台' content={<>新增「防剧透」娱乐特性，当观众在弹幕或醒目留言中输入以 <code>||</code> 开头的文本时，该文本将会被模糊化，直到您将鼠标悬停于该文本上时，该文本才会恢复正常</>} />
        <ChangeItem type='开发者' content={<>基于第 #100 次更新，如果您的模版会对用户名增加额外的阴影，现在可能会遇到阴影边缘被剪裁的问题。现在请为其增加额外的 <code>padding</code> 来解决该问题，查看内置模版「极简描边」查看用例</>} />
        <ChangeItem type='其他' content={<>历时 15 分钟的努力与研究，完成了对 HarmonyOS 的适配，成为全网第一个适配 HarmonyOS 的弹幕机。由于目前 ArkWeb 框架内核只有 114，暂时不支持 CSS 嵌套特性，其他特性完美兼容。等待内核更新至 121 后即可自动支持 CSS 嵌套特性</>} />
        <ChangeItem type='其他' content='其他细节调整' />
      </div>
    </>
  },
  {
    id: 114,
    timestamp: 'May 26, 2025, 9:03:36 AM PDT',
    pending: false,
    content: <>
      <div className='grid mb-4 gap-2'>
        <ChangeItem type='控制台' content={<>Fish Audio TTS 现在支持传入自定义模型</>} />
        <ChangeItem type='控制台' content={<>Fish Audio TTS 默认模型由 speech-1.5 改为 speech-1.6</>} />
        <ChangeItem type='控制台' content={<>新增 TTS 供应商 Cartesia，目前只有英文效果比较好，中文一般，未来可期</>} />
        <ChangeItem type='控制台' content={<>高能榜用户现在支持显示舰队指挥官头像框</>} />
      </div>
    </>
  },
  {
    id: 113,
    timestamp: 'May 25, 2025, 10:39:46 AM PDT',
    pending: false,
    content: <>
      <div className='grid mb-4 gap-2'>
        <ChangeItem type='全局' content={<>提供对「舰队指挥官」的初步支持，由于官方限制，目前仅支持弹幕事件</>} />
        <ChangeItem type='控制台' content={<>优化云端事件拉取逻辑，除了每 5 分钟一次的近期事件拉取外，现在还会每小时进行一次全量拉取，此改动可保证长时间运行的控制台在某些特殊条件下，例如断网超过 10 分钟、电脑从长时间睡眠状态唤醒后，无法拉取期间的云端事件的问题</>} />
        <ChangeItem type='LEF' content={<>优化缓存，降低缓存过期时间</>} />
        <ChangeItem type='LEF' content={<>不再兼容 laplace-event-fetcher v2.1.4 之前的版本（即 2025/03/08 之前的版本），如果您依然在使用老版本，请更新</>} />
        <ChangeItem type='开发者' content={<>现在您可以通过 <code>guard-leader--1</code> 来判断弹幕是否为舰队指挥官，从而为其定制特殊样式</>} />
      </div>
    </>
  },
]
