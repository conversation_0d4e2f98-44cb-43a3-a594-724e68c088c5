/**
 * The Global LAPLACE typings
 * Maintained by laplace.live
 */

/** Used for brain-fucked bilibili devs */
type StringBoolean = 'true' | 'false'

/** Emote list  */
export interface SimpleEmotesList {
  [key: string]: SimpleEmote
}

/**
 * Used by bmotes and remote emotes
 */
export interface SimpleEmote {
  url: string
  width?: number
  height?: number
}

/**
 * 用于 `DANMU` 事件中 @ 用户回复的 type
 * Used by:
 * - chat.laplace.chat
 */
export interface DanmakuReplyProps {
  uid: number
  uname: string
}

/**
 * LAPLACE fans medal
 */
export interface FansMedal {
  /**
   * 当没佩戴粉丝勋章时，返回 0
   */
  level: number
  /**
   * 当没佩戴粉丝勋章时，返回空字符串
   */
  name: string
  /**
   * 当前粉丝勋章的房间号，并不是所有的事件都支持，例如弹幕事件支持，礼物事件就不支持
   *
   * 不支持的时候会返回 0，返回 0 时会影响千舰、万舰的头像框和粉丝勋章图标的判断
   */
  room: number
  /**
   * 此字段通过计算事件中返回的 `uinfo.medal.guard_icon` 而来，不过 guard_icon 并不是
   * 所有的事件都会返回（例如大航海就返回空），因此仅作为兜底使用
   * 目前大航海图标只区分是否千舰，不区分万舰，只有大航海头像框区分
   */
  resolvedPerkLevel?: number
  /**
   * 粉丝勋章图标，千舰、万舰会有区别
   * @link https://i0.hdslb.com/bfs/live/98a201c14a64e860a758f089144dcf3f42e7038c.png
   * @deprecated 直接传 URL 的方式已弃用，会增大事件体积，请使用上方的 `resolvedPerkLevel` 进行判断
   */
  guardIcon?: string
  /**
   * 粉丝牌所对应主播的 UID，当 `type` 为 1 时，此时为粉丝团套票，此处的 UID 等于粉丝团 id😅
   */
  uid?: number
  /** Color in decimal color */
  // colorBorder: number;
  /** Color in decimal color */
  // colorStart: number;
  /** Color in decimal color */
  // colorEnd: number;
  /**
   * 0: 白字
   * 1: 总督
   * 2: 提督
   * 3: 舰长
   */
  guardType: number
  /**
   * 粉丝勋章是否点亮
   *
   * 1: 点亮
   * 0: 未点亮
   */
  lightened: number
  /**
   * 粉丝勋章类型，后加的属性，为了兼容性，因此为可选
   *
   * 0: 普通粉丝牌
   * 1: 大航海套票
   */
  type?: number
}

/**
 * b豆 emote props
 * @deprecated Use `SimpleBmote` instead
 */
export interface Bmote {
  emoticon_id: number
  emoji: string
  descript: string
  url: string
  width: number
  height: number
  emoticon_unique: string
  count: number
}

/**
 * `buvid` props
 */
export interface BuvidProps {
  token: string
}

/**
 * Protobuf data from danmu v2 props
 * @deprecated Not bilibili no longer returns protobuf
 */
export type ProtobufDanmuV2Props = {
  user: {
    face: string
  }
}

/** 内部连接方式 */
export type ConnectionModes = 'direct' | 'builtin' | 'qrcode' | 'open-platform'

/**
 * LAPLACE 内部 typings
 */
export declare namespace LaplaceInternal {
  /**
   * HTTP(S) 协议类型的 API
   */
  export namespace HTTPS {
    /**
     * LAPLACE Workers
     */
    export namespace Workers {
      /**
       * 获取云端弹幕机配置
       * /laplace/remote-config
       */
      export interface RemoteConfig {
        headerText?: string
        headerSubtitle?: string
        headerLink?: string
        headerLogo?: string
        headerLogoWidth?: number
        headerLogoHeight?: number
        footerExtraText?: string
        footerText?: string
        footerLink?: string
      }

      /**
       * Gatus status response
       * /laplace/network
       */
      export interface NetworkItem {
        /** "miscs" */
        group: string
        /** miscs_bilibili-fingerprint-reporter */
        key: string
        /** bilibili-fingerprint-reporter */
        name: string
        results: {
          status: number
          duration: number
          hostname?: string
          success: boolean
          /** 2024-04-28T08:22:07.19385135Z */
          timestamp: string
          errors?: string[]
          conditionResults?: {
            /** "[CERTIFICATE_EXPIRATION] > 48h" */
            condition: string
            success: boolean
          }[]
        }[]
      }

      /**
       * Login Sync info
       * A subset of `BilibiliInternal.HTTPS.Prod.LiveGetUserInfo`
       */
      export interface LoginSyncInfo {
        /** 0: 成功 */
        code: number
        message: string
        data: {
          isLogin: boolean
          face: string
          mid: number
          uname: string
          /** The actual `buvid` */
          ack: string
          fetchMode: 'login-sync' | 'anonymous' | 'built-in'
          ackMode: 'guest' | 'passthrough' | 'builtin-passthrough'
          fetcher: number
          syncServer: boolean
        }
      }

      /**
       * 获取云端表情包
       * /laplace/remote-emotes
       */
      export interface PspRemoteEmotesProps {
        /**
         * - 0: valid result
         * - 403: not valid
         */
        status: number
        data: {
          emoji: {
            server_base: string
            map: {
              [key: string]: string
            }
          }
        }
      }

      /**
       * 直播间排名列表，由外部 API 聚合而来
       * /laplace/ranking
       */
      export interface RankingItem {
        /**
         * @minimum 1
         */
        _laplace_rank: number
        cate: string
        uname: string
        uid: number
        face: string
        cover: string
        system_cover: string
        start_time: number
        roomid: number
        watched?: number
        watched_avg?: number
        ten_minutes_counter: number
        interaction_avg?: number
        income?: number
        income_avg?: number
        danmakus?: number
        danmakus_avg?: number
        title: string
        tags?: string[]
        commentCount?: number
        lastLiveIncome?: number
        /**
         * laplace/loveava 独有的热度/人气字段
         */
        count?: number
        /**
         * 平均观看人数
         */
        count_avg?: number
      }

      /** 关注 API 的数据库扩展 */
      interface FollowingResponseExtended extends BilibiliInternal.HTTPS.Prod.GameCenterFollowingList {
        /**
         * 最后 KV 缓存写入时间戳
         */
        last: number
        /**
         * 数据来源
         */
        from: 'cache' | 'origin'
      }

      /**
       * 由 vtbs 整理来的直播间开播状态
       */
      interface LiveState {
        name: string
        room: number
        liveStatus: number
        lastLive: {
          time: number
        }
        title: string
        time: number
        liveStartTime: number
      }

      /**
       * UID by Room
       */
      interface UidByRoom {
        /**
         * 0 正常，404 未找到直播间
         */
        status: 0 | 404
        uid: number
      }

      /**
       * 哔哩哔哩装扮列表
       */
      interface Suit {
        id: number
        name: string
        description: string
        startTime: number
        totalPurchaseCount: number
        user: number
        username: string
        owner: number
        image_cover: string | null
        createdAt: string
        updatedAt: string
        ownerDetails: {
          id: number
          room: number
          username: string
          guild: null
          avatar: string
          updatedAt: string
        } | null
      }

      /**
       * 哔哩哔哩收藏集列表
       */
      interface Collection {
        id: number
        name: string
        description: string
        startTime: number
        relatedUsers: number[]
        totalPreorderCount: number
        totalPurchaseCount: number
        /** @deprecated use `lottery_image` */
        act_square_img: string
        lottery_image: string
        createdAt: string
        updatedAt: string
      }

      /**
       * 日程表项目
       * @link https://laplace.live/schedule
       */
      export interface WeekItem {
        /** 年份，例如 2024 */
        year: number
        /** 当前周数，例如 19 */
        week: number
        /** 哔哩哔哩动态对应 ID，只需要填入 ID 即可 */
        bilibili_url?: string
        events: EventItem[]
      }

      /**
       * Item of WeekItem events
       * 此处 type 为简化版 `string`，在 laplace 本体仓库中有详细定义
       */
      export interface EventItem {
        /** 日期字符串，格式 YYYY-MM-DD */
        date: string
        /** 日程类型 */
        type: string
        /** 日程标题 */
        title: string
        /** 录播链接，此应填入完整 URL */
        rec?: string
        /** 是否被取消，此字段用于 iCal 日历 */
        cancelled?: boolean
        /** 取消原因 */
        reason?: string
        /** 录播是否为第三方录入 */
        fans_rec?: boolean
      }

      /** 多媒体可用语言 */
      export type MediaLanguages = 'zh' | 'en' | 'ja' | 'yue' | 'kr' | 'ru' | 'es' | 'fr'

      /**
       * 多媒体项目
       */
      export interface MediaItem {
        ver: number
        /** 当前媒体语言，例如 `en` */
        lang: MediaLanguages
        /** 日期 */
        date: string
        /** 是否有切片，完整 URL */
        clips?: string
        /** 标题 */
        title: string
        /** alternate 名称，通常用于提供标题的无翻译原文 */
        altName?: string
        /** 是否为官方发布 */
        official?: boolean
        /** 作者/艺术家 */
        author: string
        /** 备注内容 */
        note?: string
        /** 是否为本站首发，LAPLACE 内部字段 */
        exclusive?: boolean
        /**
         * 标签
         * 如果一场直播内唱了多次同一首歌曲，会导致声称的文件名重复，用此属性定义额外后缀防止冲突
         */
        tags?: string
      }

      /**
       * 时间线项目
       * @link https://laplace.live/timeline
       */
      export interface TimelineItem {
        /**
         * - `hitokoto`: 一言
         * - `video`: 视频
         * - `event`: 事件
         */
        type: 'hitokoto' | 'video' | 'event'
        /** 内容 */
        content: string
        /** 事件关联人物 */
        from_who: string
        /** 是否含有音声，LAPLACE 内部字段 */
        hasVoice?: boolean
        /** 音声对应文件名，LAPLACE 内部字段 */
        voiceFilename?: string
        /** 事件发生点 */
        from?: string
        /** 事件消息来源 */
        source?: string
        /** 事件日期 */
        date?: string
      }

      /**
       * 开发日志项目
       * @link https://laplace.live/blog
       */
      export interface BlogItem {
        id: number
        date: string
        title?: string
        content: string | unknown
        /** 给 Atom RSS 用的纯文本输出 */
        plain?: string
        author?: string
        tags?: string[]
      }

      /**
       * SuperChat FoS
       * @link /laplace/superchat-fos
       */
      export interface SuperChatFoS {
        code: number
        /** 报错时会返回 */
        message?: string
        content: string
        moderated: boolean
      }

      /**
       * 用户信息
       * @link /bilibili/bilibili-user/<uid>
       */
      export interface BilibiliUser {
        /** 对应 UID */
        id: number
        username: string
        /** 之前泄漏的工会字段 */
        guild: string
        room: number
        liveFansCount: number
        avatar: string | null
        /** 2024-12-15T03:44:13.788Z */
        updatedAt: string
        /** 工会详细信息 */
        guildDetails: {
          id: number
          name: string
        } | null
      }

      /**
       * 礼物配置信息
       */
      export interface RoomGiftItem extends BilibiliInternal.HTTPS.Prod.RoomGiftItemPick {
        /** 出现的时间 */
        firstSeen?: Date
        /** 出现的直播间 */
        firstSeenRoom?: number
      }

      /**
       * 礼物配置列表
       * @link /bilibili/room-gift-config/25034104?list=1
       */
      export interface RoomGiftConfigList {
        code: number
        message: string
        ttl: number
        gift_ttl: number
        gift_version: number
        data: {
          list: RoomGiftItem[]
        }
      }

      /**
       * 获取直播分区列表
       * @link /bilibili/live-areas
       */
      export interface LiveAreas {
        /** 0 代表成功 */
        code: number
        /** 成功时返回 "success" */
        message: string
        /** 直播分区列表 */
        data: LiveAreaItem[]
      }

      /**
       * 直播分区项
       */
      export interface LiveAreaItem {
        /** 分区 ID */
        id: string
        /** 分区名称 */
        name: string
        /** 父分区 ID */
        parentId: string
        /** 父分区名称 */
        parentName: string
        /** 完整分区名称，格式为 `${parentName}-${name}` */
        cate: string
        /** 分区图标 URL */
        icon: string
      }

      /**
       * Response interface for the transformed LiveGiftEffect data
       * Used by frontend to consume the simplified gift effect data
       * @example /bilibili/live-gift-effect/25034104
       */
      export interface LiveGiftEffect {
        code: number
        message: string
        data: LiveGiftEffectItem[]
      }

      /**
       * Simplified gift effect item with only necessary fields
       */
      export interface LiveGiftEffectItem {
        type: number
        id: number
        bind_gift_ids: number[]
        video: string
        /** json config URL, only the hash part */
        json: string
      }
    }

    /** Migrated from experiments.sparanoid.net */
    export namespace Experiments {
      /**
       * fixer.io API
       * @link http://data.fixer.io/api/latest?access_key=
       * */
      interface Fixer {
        success: boolean
        timestamp: number
        base: string
        date: string
        rates: FixerRates
      }

      /** Props of Fixer */
      interface FixerRates {
        [key: string]: number
      }

      /**
       * Spotify API
       */
      export namespace Spotify {
        /**
         * Spotify get access token
         * @link https://developer.spotify.com/documentation/web-api/tutorials/getting-started
         */
        interface Token {
          access_token: string
          token_type: 'Bearer'
          expires_in: 3600
        }

        /**
         * Search result
         */
        export interface Search {
          tracks: Tracks
        }

        /** Track list of `Search` */
        export interface Tracks {
          href: string
          items: Track[] | []
          limit: number
          next: string | null
          offset: number
          previous: null | null
          total: number
        }

        /** Track item of `Tracks` */
        export interface Track {
          album: Album
          artists: Artist[]
          available_markets: string[]
          disc_number: number
          duration_ms: number
          explicit: boolean
          external_ids: ExternalIDS
          external_urls: ExternalUrls
          href: string
          id: string
          is_local: boolean
          name: string
          popularity: number
          preview_url: null
          track_number: number
          type: string
          uri: string
        }

        /** Album item of `Track` */
        export interface Album {
          album_type: string
          artists: Artist[]
          available_markets: string[]
          external_urls: ExternalUrls
          href: string
          id: string
          images: Image[] | []
          name: string
          release_date: Date
          release_date_precision: string
          total_tracks: number
          type: string
          uri: string
        }

        /** Artist props */
        export interface Artist {
          external_urls: ExternalUrls
          href: string
          id: string
          name: string
          type: string
          uri: string
        }

        /** External URLs */
        export interface ExternalUrls {
          spotify: string
        }

        /** 专辑封面 */
        export interface Image {
          height: number
          url: string
          width: number
        }

        /** External IDs */
        export interface ExternalIDS {
          isrc: string
        }
      }

      /** Apple API */
      export namespace Apple {
        /** Apple Music API */
        export interface Music {
          results: MusicSearchResults
          meta: MusicSearchMeta
        }

        /** Props of `Music` */
        export interface MusicSearchMeta {
          results: MusicSearchMetaResults
        }

        /** Props of `MusicSearchMeta` */
        export interface MusicSearchMetaResults {
          order: string[]
          rawOrder: string[]
        }

        /** Props of `Music` */
        export interface MusicSearchResults {
          songs: Songs
        }

        /** Props of `Songs` */
        export interface Songs {
          href: string
          next: string
          data: Datum[]
        }

        /** Props of `Datum` */
        export interface Datum {
          id: string
          type: string
          href: string
          attributes: Attributes
        }

        /** Props of `Attributes` */
        export interface Attributes {
          albumName: string
          genreNames: string[]
          trackNumber: number
          durationInMillis: number
          releaseDate: Date
          isrc: string
          artwork: Artwork
          playParams: PlayParams
          url: string
          discNumber: number
          hasCredits: boolean
          isAppleDigitalMaster: boolean
          hasLyrics: boolean
          name: string
          previews: Preview[]
          artistName: string
          composerName?: string
        }

        /** Props of `Attributes` */
        export interface Artwork {
          width: number
          height: number
          url: string
          bgColor: string
          textColor1: string
          textColor2: string
          textColor3: string
          textColor4: string
        }

        /** Props of `Attributes` */
        export interface PlayParams {
          id: string
          kind: string
        }

        /** Props of `Attributes` */
        export interface Preview {
          url: string
        }
      }

      /**
       * Address Completion
       * @link /experiments/address-completion
       */
      export interface AddressCompletion {
        code: number
        /** 报错时会返回 */
        message?: string
        data: {
          /** 地址信息，通常为 `poiInfo` + `houseInfo` */
          addrInfo: string
          /** 门牌号，通常为 address line 2 */
          houseInfo: string
          /** 详细地址，通常为 address line 1 */
          poiInfo: string
          /** 街道 */
          street: string
          /** 城市 */
          city: string
          /** 地区 */
          district: string
          /** 省 */
          province: string
          /** 收件人 */
          person: string
          /** 手机/电话 */
          phone: string
        }
      }
    }
  }

  /**
   * WebSocket 协议
   */
  export namespace WebSocket {
    /**
     * 线上环境
     */
    export namespace Prod {
      /**
       * 内置系统消息
       */
      export interface SYSTEM_MSG {
        timestamp: number
        username?: string
        message?: string
      }
    }
  }
}

/**
 * 哔哩哔哩内部 typings
 */
export declare namespace BilibiliInternal {
  /**
   * HTTP(S) 协议类型的 API
   */
  namespace HTTPS {
    /**
     * 线上环境
     */
    namespace Prod {
      /**
       * Nav
       * 获取用户基本信息
       * @link https://api.bilibili.com/x/web-interface/nav
       */
      export interface Nav {
        /**
         * - `0`: 登录状态
         * - `-101`: 未登录
         */
        code: number
        message: string
        ttl: number
        data: {
          isLogin: boolean
          email_verified: number
          face: string
          face_nft: number
          face_nft_type: number
          level_info: {
            current_level: number
            current_min: number
            current_exp: number
            next_exp: number
          }
          mid: number
          mobile_verified: number
          money: number
          moral: number
          official: {
            role: number
            title: string
            desc: string
            type: number
          }
          officialVerify: {
            type: number
            desc: string
          }
          pendant: {
            pid: number
            name: string
            image: string
            expire: number
            image_enhance: string
            image_enhance_frame: string
            n_pid: number
          }
          scores: number
          uname: string
          vipDueDate: number
          vipStatus: number
          vipType: number
          vip_pay_type: number
          vip_theme_type: number
          vip_label: NavLabel
          vip_avatar_subscript: number
          vip_nickname_color: string
          vip: {
            type: number
            status: number
            due_date: number
            vip_pay_type: number
            theme_type: number
            label: NavLabel
            avatar_subscript: number
            nickname_color: string
            role: number
            avatar_subscript_url: string
            tv_vip_status: number
            tv_vip_pay_type: number
            tv_due_date: number
            avatar_icon: {
              icon_resource: {}
            }
          }
          wallet: {
            mid: number
            bcoin_balance: number
            coupon_balance: number
            coupon_due_time: number
          }
          has_shop: boolean
          shop_url: string
          allowance_count: number
          answer_status: number
          is_senior_member: number
          wbi_img: {
            img_url: string
            sub_url: string
          }
          is_jury: boolean
          /**
           * 由 buvid 请求获取，目前已改名为 ack，用于混淆请求发，防止被滥用
           */
          ack: string
          /**
           * 当前请求的获取模式，built-in 为 cookieCloud 内置；anonymous 为匿名
           */
          fetchMode: string
          /**
           * 获取当前请求所用的 uid，如果是匿名请求则为 0
           */
          fetcher: number
          /**
           * 当前请求的 buvid3 获取模式
           */
          ackMode: string
          /**
           * Custom CookieCloud sync server
           */
          syncServer?: string
        }
      }

      /** Props of `NavLabel` */
      export interface NavLabel {
        path: string
        text: string
        label_theme: string
        text_color: string
        bg_style: number
        bg_color: string
        border_color: string
        use_img_label: boolean
        img_label_uri_hans: string
        img_label_uri_hant: string
        img_label_uri_hans_static: string
        img_label_uri_hant_static: string
      }

      /**
       * GetDanmuInfo
       * 用户获取直播间弹幕 WS 连接
       *
       * Mar 29, 2025 发现需要传入 w_rid 和 wts，目前不传也没什么问题
       * @example https://api.live.bilibili.com/xlive/web-room/v1/index/getDanmuInfo?id=21756924&type=0&w_rid=a51251c3b57edf22b0e54cb13902aa5b&wts=1743276182
       *
       * May 20, 2025 发现需要传入 web_location 字段了，目前不传也没什么问题
       * @example https://api.live.bilibili.com/xlive/web-room/v1/index/getDanmuInfo?id=30858592&type=0&web_location=444.8&w_rid=2067505b9fe82c4a46ebfad51d6f47a2&wts=1747742639
       *
       * @link https://api.live.bilibili.com/xlive/web-room/v1/index/getDanmuInfo?id=${room_id}&type=0
       */
      export interface GetDanmuInfo {
        /**
         * - 0: 正常
         * - -352: 封控
         */
        code: number
        message: string
        ttl: number
        /** 封控或错误时此字段不存在 */
        data?: {
          group: string
          business_id: number
          refresh_row_factor: number
          refresh_rate: number
          max_delay: number
          token: string
          host_list: DanmuWebSocketHost[]
          /**
           * 由 buvid 请求获取，目前已改名为 ack，用于混淆请求发，防止被滥用
           */
          ack: string
          /**
           * @deprecated use `ack`
           */
          buvid: string
          /**
           * 当前请求的获取模式，built-in 为 cookieCloud 内置；anonymous 为匿名
           */
          fetchMode: string
          /**
           * 获取当前请求所用的 uid，如果是匿名请求则为 0
           */
          fetcher: number
          /**
           * 当前请求的 buvid3 获取模式
           */
          ackMode: string
          /**
           * @deprecated use `ackMode`
           */
          buvidMode: string
          /**
           * Custom CookieCloud sync server
           */
          syncServer?: string
        }
      }

      /**
       * Danmaku WebSocket host info
       */
      export interface DanmuWebSocketHost {
        host: string
        port: number
        wss_port: number
        ws_port: number
      }

      /**
       * 从直播间 DOM 中提取的直播间聚合基本信息，存在于 DOM 中，目前通过正则提取，代替之前的 `GetInfoByRoom`
       *
       * `window.__NEPTUNE_IS_MY_WAIFU__ = {...`
       *
       * @link https://live.bilibili.com/25034104
       */
      export interface RoomInfoDom {
        roomInitRes: GetRoomPlayInfo
        roomInfoRes: GetInfoByRoom
      }

      /** GiftMemoryInfoClass in `RoomInfoRes`  */
      export interface GiftMemoryInfoClass {
        list:
          | {
              /**
               * `seven-rank`, `guard`
               */
              type: string
              /**
               * `高能用户`, `大航海`
               */
              desc: string
              isFirst: number
              isEvent: number
              eventType: string
              listType: string
              apiPrefix: string
              /**
               * `room_7day`
               */
              rank_name: string
            }[]
          | null
      }

      /**
       * Global user Medals used by live APIs
       * 部分 WebSocket 事件中也会使用此 interface：
       * - DANMU_MSG
       */
      export interface MedalInfo {
        // ie. 7996451
        color: number
        color_border: number
        color_end: number
        color_start: number
        /**
         * 圆形大航海图标，并不会总是返回，例如舰长此处为空，不知道原因
         * @link https://i0.hdslb.com/bfs/live/98a201c14a64e860a758f089144dcf3f42e7038c.png
         */
        guard_icon: string
        /**
         * 0: 白字，也可能是舰长套票
         * 1: 总督
         * 2: 提督
         * 3: 舰长
         */
        guard_level: number
        /**
         * 没有时返回 `''`
         */
        honor_icon: string
        /**
         * 未知，通常为 0
         */
        id: number
        /**
         * 粉丝牌是否点亮，1 为点亮
         */
        is_light: number
        /**
         * 粉丝牌等级，也可能是 0
         */
        level: number
        /**
         * 粉丝牌名称
         */
        name: string
        /**
         * 粉丝牌所对应主播的 UID，当 `typ` 为 1 时，此时为粉丝团套票，此处的 ruid 等于粉丝团 id😅
         */
        ruid: number
        /**
         * 未知
         */
        score: number
        /**
         * 类型，通常为 0，为 1 时为大航海套票
         */
        typ: number
        /** 通常为 0 */
        user_receive_count: number
        /** "#5FC7F4FF" */
        v2_medal_color_border: string
        v2_medal_color_end: string
        v2_medal_color_level: string
        v2_medal_color_start: string
        v2_medal_color_text: string
      }

      /** UserRankTabItem of `RoomInfoRes` */
      export interface UserRankTabItem {
        type: string
        title: string
        status: number
        default: number
        comment: string
        desc_url: string
        switch:
          | {
              text: string
              switch: string
              ui_type: {
                op_button_text: number
                rank_prefix: number
                refresh_entry?: number
                show_score: number
              }
              comment: string
            }[]
          | null
        sub_tab: UserRankTabItem[] | null
      }

      /**
       * GetInfoByRoom
       * 获取直播间基本信息，可用于获取直播间号对应的 UID
       *
       * @example https://api.live.bilibili.com/xlive/web-room/v1/index/getInfoByRoom?room_id=21622811&web_location=444.8&w_rid=da46145ca70fbf60e794a2ac3cdfa845&wts=1743236901
       *
       * @link https://api.live.bilibili.com/xlive/web-room/v1/index/getInfoByRoom?room_id=
       * @link https://live.bilibili.com/21696950 - aza 直播间还在用
       *
       * @since 2024-05-24
       *
       * @deprecated 目前b站 **可能** 不再使用该 API，有停用风险，只有个别活动直播间（特殊背景样式，例如德云色）还会继续用
       * 通过 /blanc/21696950 可暂时强制恢复该请求调用
       *
       * @link https://live.bilibili.com/blanc/21696950
       *
       */
      export interface GetInfoByRoom {
        /**
         * - 0: 正常
         * - 19002005: 房间已加密
         */
        code: number
        message: string
        ttl?: number
        /** 当 code !== 0 时此字段不存在😅 */
        data?: RoomInfoData
      }

      /** Data of `GetInfoByRoom` */
      export interface RoomInfoData {
        /**
         * 由 laplace-workers room-info APi 额外传入的字段，主要给 laplace-chat 控制台使用
         *
         * @deprecated Jun 1, 2025 不再魔改官方返回，直接从官方的不知道什么时候加的 room_rank_info 拿
         */
        perf_info?: {
          /** 高能榜 */
          online: number
          users: BilibiliInternal.HTTPS.Prod.ContributionRankUser[]
        }

        room_info: {
          uid: number
          room_id: number
          /** 短房间名，为 0 时则代表当前房间无短房间号 */
          short_id: number
          /** 直播间标题 */
          title: string
          /** 直播间封面 */
          cover: string
          /** 标签 */
          tags: string
          background: string
          description: string
          live_status: 0 | 1
          /** 直播开始时间戳 */
          live_start_time: number
          live_screen_type: number
          lock_status: number
          lock_time: number
          hidden_status: number
          hidden_time: number
          /** 直播分区 ID */
          area_id: number
          /** 直播分区名称 */
          area_name: string
          /** 父级分区 ID */
          parent_area_id: number
          /** 父级分区名称 */
          parent_area_name: string
          /** 直播关键帧 */
          keyframe: string
          special_type: number
          up_session: string
          pk_status: number
          is_studio: boolean
          pendants: {
            /** 当前主播在直播间内的头像框 */
            frame: {
              name: string
              value: string
              desc: string
            }
          }
          on_voice_join: number
          /** 不知道是干嘛的字段，并非高能，比高能高很多 */
          online: number
          room_type: {
            '3-21': number
            '3-50': number
          }
          sub_session_key: string
          live_id: number
          live_id_str: string
          official_room_id: number
          official_room_info: null
          voice_background: string
        }
        anchor_info: {
          base_info: {
            uname: string
            face: string
            gender: string
            official_info: {
              role: number
              title: string
              desc: string
              is_nft: number
              nft_dmark: string
            }
          }
          /** 当前主播直播间信息 */
          live_info: {
            /** UP主直播等级 */
            level: number
            level_color: number
            score: number
            upgrade_score: number
            current: number[]
            next: number[]
            rank: string
          }
          relation_info: {
            /** 关注人数 */
            attention: number
          }
          /**
           * 当前主播粉丝勋章信息
           * 这逼玩意可能返回null，咱也不知道什么时候返回😅，会直接导致 laplace-chat 控制台报错
           */
          medal_info: {
            /** 粉丝勋章名 */
            medal_name: string
            medal_id: number
            /** 粉丝团数量 */
            fansclub: number
          } | null
          gift_info: {
            price: number
            price_update_time: number
          }
        }
        news_info: {
          uid: number
          ctime: Date
          content: string
        }
        rankdb_info: {
          roomid: number
          rank_desc: string
          color: string
          h5_url: string
          web_url: string
          timestamp: number
        }
        area_rank_info: {
          areaRank: {
            index: number
            rank: string
          }
          liveRank: {
            rank: string
          }
        }
        battle_rank_entry_info: null
        tab_info: GiftMemoryInfoClass
        activity_init_info: {
          eventList: any[]
          weekInfo: {
            bannerInfo: null
            giftName: null
          }
          giftName: null
          lego: {
            timestamp: number
            config: string
          }
        }
        voice_join_info: {
          status: {
            open: number
            anchor_open: number
            status: number
            uid: number
            user_name: string
            head_pic: string
            guard: number
            start_at: number
            current_time: number
          }
          icons: {
            icon_close: string
            icon_open: string
            icon_wait: string
            icon_starting: string
          }
          web_share_link: string
        }
        ad_banner_info: {
          data: null
        }
        skin_info: {
          id: number
          skin_name: string
          skin_config: string
          show_text: string
          skin_url: string
          start_time: number
          end_time: number
          current_time: number
        }
        web_banner_info: {
          id: number
          title: string
          left: string
          right: string
          jump_url: string
          bg_color: string
          hover_color: string
          text_bg_color: string
          text_hover_color: string
          link_text: string
          link_color: string
          input_color: string
          input_text_color: string
          input_hover_color: string
          input_border_color: string
          input_search_color: string
        }
        lol_info: null
        pk_info: null
        battle_info: null
        silent_room_info: {
          type: string
          level: number
          second: number
          expire_time: number
        }
        switch_info: {
          close_guard: boolean
          close_gift: boolean
          close_online: boolean
          close_danmaku: boolean
        }
        record_switch_info: null
        room_config_info: {
          dm_text: string
        }
        gift_memory_info: GiftMemoryInfoClass
        new_switch_info: { [key: string]: number }
        super_chat_info: {
          status: number
          jump_url: string
          icon: string
          ranked_mark: number
          message_list: any[]
        }
        online_gold_rank_info_v2: RankOnlineInfoItem
        dm_brush_info: {
          min_time: number
          brush_count: number
          slice_count: number
          storage_time: number
          is_hide_anti_brush?: number
        }
        dm_emoticon_info: {
          is_open_emoticon: number
          is_shield_emoticon: number
        }
        dm_tag_info: {
          dm_tag: number
          platform: null
          extra: string
          dm_chronos_extra: string
          dm_mode: null
          dm_setting_switch: number
          material_conf: null
        }
        topic_info: {
          topic_id: number
          topic_name: string
        }
        game_info: {
          game_status: number
        }
        watched_show: {
          switch: boolean
          num: number
          text_small: string
          text_large: string
          icon: string
          icon_location: number
          icon_web: string
        }
        topic_room_info: {
          interactive_h5_url: string
          watermark: number
        }
        show_reserve_status: boolean
        second_create_info: null
        play_together_info: null
        cloud_game_info: {
          is_gaming: number
        }
        like_info_v3: {
          total_likes: number
          click_block: boolean
          count_block: boolean
          guild_emo_text: string
          guild_dm_text: string
          like_dm_text: string
          hand_icons: string[]
          dm_icons: string[]
          eggshells_icon: string
          count_show_time: number
          process_icon: string
          process_color: string
          report_click_limit: number
          report_time_min: number
          report_time_max: number
          icon: string
          cooldown: number
          hand_use_face: boolean
          guide_icon_urls: string[]
          guide_icon_ratio: number
        }
        live_play_info: {
          show_widget_banner: boolean
          show_left_entry: boolean
          widget_version: number
        }
        multi_voice: {
          switch_status: number
          members: any[]
          mv_role: number
          seat_type: number
          invoking_time: number
          version: number
          pk: null
          biz_session_id: string
          mode_details: null
          hat_list: null
          battle_info: null
        }
        popular_rank_info: {
          rank: number
          countdown: number
          timestamp: number
          url: string
          on_rank_name: string
          rank_name: string
          rank_by_type: number
          rank_name_by_type: string
          on_rank_name_by_type: string
          url_by_type: string
          default_url: string
        }
        new_area_rank_info: {
          items: {
            conf_id: number
            rank_name: string
            uid: number
            rank: number
            icon_url_blue: string
            icon_url_pink: string
            icon_url_grey: string
            jump_url_link: string
            jump_url_pc: string
            jump_url_pink: string
            jump_url_web: string
          }[]
          rotation_cycle_time_web: number
        }
        gift_star: {
          show: boolean
          display_widget_ab_group: number
        }
        progress_for_widget: {
          gift_star_process: {
            task_info: {
              start_date: number
              process_list: {
                gift_id: number
                gift_img: string
                gift_name: string
                completed_num: number
                target_num: number
              }[]
              finished: boolean
              ddl_timestamp: number
              version: number
              reward_gift: number
              reward_gift_img: string
              reward_gift_name: string
              level_info: {
                star_name: string
                level_tip: string
                level_img: string
                level_id: number
              }
            }
            preload_timestamp: number
            preload: boolean
            preload_task_info: null
            widget_bg: string
            jump_schema: string
            ab_group: number
          }
          wish_process: null
          star_knight: null
          collection_praise_process: {
            id: number
            uid: number
            target_praise: number
            current_praise: number
            start_time: number
            end_time: number
            benefit: string
            isSuccess: boolean
            exist: boolean
            audit_status: number
            jump_url: string
            current_praise_text: string
            icon_url: string
            live_id: string
          }
        }
        revenue_demotion: {
          global_gift_config_demotion: boolean
        }
        revenue_material_md5: null
        block_info: {
          block: boolean
        }
        danmu_extra: {
          screen_switch_off: boolean
        }
        video_connection_info: null
        player_throttle_info: {
          status: number
          normal_sleep_time: number
          fullscreen_sleep_time: number
          tab_sleep_time: number
          prompt_time: number
        }
        /** 舰队信息，在某种特殊情况下，下列对象可能为 null😅，例如直播间 5440 */
        guard_info: {
          count: number
          /** 当前舰长数所在层级，例如百舰主播为 100，千舰主播为 1000 */
          anchor_guard_achieve_level: number
        } | null
        hot_rank_info: null
        room_rank_info: {
          anchor_rank_entry: null
          user_rank_entry: {
            user_contribution_rank_entry: {
              item: ContributionRankUser[]
              count: number
              show_max: number
              count_text: string
              non_expandable: boolean
            }
          }
          user_rank_tab_list: {
            tab: UserRankTabItem[]
          }
        }
        dm_reply: {
          show_reply: boolean
        }
        dm_combo: null
        dm_vote: null
        location: null
        interactive_game_tag: {
          action: number
          game_id: string
          game_name: string
        }
        video_enhancement: {
          title: string
          desc: string
          default_switch_status: number
          highest_quality: number
          is_enabled: boolean
        }
        guard_leader: {
          uid: number
          name: string
          face: string
          jump_url: string
          /**
           * 成为舰队指挥官，解锁房间冠名权益
           */
          text: string
          rank_top_icon1: string
          rank_top_icon2: string
          rank_top_background_url1: string
          rank_top_background_url2: string
          background_url: string
          anchor_background_url: string
          input_background_url: string
          newly: number
          entry_effect_id: number
          show: number
          rank_top_background_light_url1: string
          rank_top_background_light_url2: string
          display_src: string
          avatar_src: string
          icon_src: string
        }
        /**
         * 公开匿名房间？需深入研究
         * @since 2025-04-12
         */
        room_anonymous: {
          open_anonymous: boolean
        }
        tab_switches: {
          subtitle: number
          realtime_data: null
        }
        universal_interact_info: null
        pk_info_v2: null
        area_mask_info: null
        xtemplate_config: {
          dm_brush_info: {
            landScape: null
            verticalscreen: {
              min_time: number
              brush_count: number
              slice_count: number
              storage_time: number
              is_hide_anti_brush?: number
            }
          }
          dm_speed_info: {
            landScape: null
            verticalscreen: {
              valley: {
                consumetime: number
                consumecount: number
                animationtime: number
              }
              peak: {
                consumetime: number
                consumecount: number
                animationtime: number
              }
              proportion: number
              interval: number
            }
          }
          dm_pool_info: {
            landScape: null
            verticalscreen: {
              master_ceiling: number
              master_count: number
              guest_config: {
                score_floor: number
                score_ceiling: number
                dm_max: number
                consume: number
              }[]
              timeout: number
              unusual_score: number
            }
          }
        }
        dm_activity: {
          activity_list: null
          ts: number
        }
        dm_interaction_ab: { [key: string]: number }
        guard_intimacy_rank_status: {
          guard_rank_new_ab: number
          guard_rank_new_total_status: number
          guard_rank_new_month_status: number
          guard_rank_new_week_status: number
        }
      }

      /** Online user info item of `GetInfoByRoom` */
      interface RankOnlineInfoItem {
        list:
          | {
              uid?: number
              face?: string
              uname?: string
              score?: string
              rank?: number
              guard_level?: number
              wealth_level?: number
              is_mystery?: boolean
              uinfo?: Uinfo
            }[]
          | null
        count: number
        count_text: string
      }

      /** General user info */
      export interface Uinfo {
        uid: number
        base: UinfoBase
        medal: MedalInfo | null
        wealth: {
          level: number
          dm_icon_key: string
        } | null
        title: {
          old_title_css_id: string
          title_css_id: string
        } | null
        /**
         * - 在 room-info 的 room_rank_info 中会返回 null
         * - queryContributionRank 中会返回实际值
         */
        guard: {
          level: number
          expired_str: string
        } | null
        uhead_frame: {
          id: number
          frame_img: string
        } | null
        guard_leader: null
      }

      /** Base info of Uinfo */
      export interface UinfoBase {
        name: string
        face: string
        name_color: number
        is_mystery: boolean
        risk_ctrl_info: {
          name: string
          face: string
        } | null
        origin_info: {
          name: string
          face: string
        }
        official_info?: {
          role: number
          title: string
          desc: string
          type: number
        }
        name_color_str?: string
      }

      /**
       * 直播高能榜 v2
       * @link https://api.live.bilibili.com/xlive/general-interface/v1/rank/queryContributionRank?ruid=${room_info.uid}&room_id=${room_id}&page=1&page_size=50&type=online_rank&switch=contribution_rank
       * @since 2024-05-24
       */
      export interface QueryContributionRank {
        code: number
        message: string
        ttl: number
        data: {
          count: number
          item: ContributionRankUser[]
          own_info: {
            uid: number
            name: string
            face: string
            rank: number
            score: number
            rank_text: string
            need_score: number
            medal_info: QueryContributionRankMedalInfo
            guard_level: number
            wealth_level: number
            score_lead: number
            score_behind: number
            is_mystery: boolean
            uinfo: Uinfo
          }
          tips_text: string
          count_format: string
          desc_format: string
          config: {
            deadline_ts: number
            value_text: string
          }
        }
      }

      /** 高能用户 */
      export interface ContributionRankUser {
        uid: number
        name: string
        face: string
        rank: number
        score: number
        medal_info: QueryContributionRankMedalInfo
        /**
         * 在 room-info 的 room_rank_info 中永远返回 0 😅
         *
         * 但是在 medal_info 中此时会无视观众当前佩戴的粉丝牌，只展示当前直播间所对应的粉丝牌
         * 因此可以从 medal_info 中判断大航海等级
         */
        guard_level: number
        wealth_level: number
        is_mystery: boolean
        uinfo: Uinfo
      }

      /** 高能中的粉丝牌 */
      export interface QueryContributionRankMedalInfo {
        guard_level: number
        medal_color_start: number
        medal_color_end: number
        medal_color_border: number
        medal_name: string
        level: number
        target_id: number
        is_light: number
      }

      /**
       * GetCardByMid
       * @link https://account.bilibili.com/api/member/getCardByMid?mid=
       * @deprecated Mar 19, 2025 左右，该接口直接返回 404，不再可用
       */
      export interface GetCardByMid {
        /** 当用户不存在时，此字段不存在 */
        ts?: number
        code: number
        /** 当用户不存在时，此字段存在：`"用户不存在"` */
        message?: string
        /** 当用户不存在时，此字段不存在 */
        card?: {
          mid: string
          name: string
          approve: boolean
          sex: string
          rank: string
          face: string
          /**
           * Aug 14, 2024：下午某个时间开始不再返回硬币数，统一返回 0
           * 自己登录也没用，因为此 API 官方已不再使用，新 API 为（最高封控）：
           * https://api.bilibili.com/x/space/wbi/acc/info?mid=
           */
          coins: 0
          DisplayRank: string
          /**
           * Aug 14, 2024：下午某个时间开始不再返回注册时间，统一返回 0
           */
          regtime: 0
          spacesta: number
          place: string
          birthday: Date
          sign: string
          description: string
          article: number
          attentions: any[]
          fans: number
          friend: number
          attention: number
          level_info: {
            next_exp: number
            current_level: number
            current_min: number
            current_exp: number
          }
          pendant: {
            pid: number
            name: string
            image: string
            expire: number
          }
          official_verify: {
            type: number
            desc: string
          }
        }
      }

      /**
       * Card
       * 用户基本信息，用来代替 `GetCardByMid`
       *
       * 在 https://space.bilibili.com/ 个人空间中，动态下的评论，鼠标移动到评论头像后会触发此 api
       *
       * web_location 为随机，机制未知，可以是 333.999、333.1387 等多个不同值，随机传即可，似乎不影响封控
       *
       * @link https://api.bilibili.com/x/web-interface/card?mid=2763&photo=true&web_location=333.1387
       */
      export interface Card {
        code: number
        /**
         * 正常时，此字段返回 `"0"` 😅
         */
        message: string
        ttl: number
        data: {
          card: {
            mid: string
            name: string
            approve: boolean
            sex: string
            rank: string
            face: string
            face_nft: number
            face_nft_type: number
            DisplayRank: string
            /**
             * 与 `GetCardByMid` 一样，现在返回 0
             */
            regtime: number
            spacesta: number
            birthday: string
            place: string
            description: string
            article: number
            attentions: any[]
            fans: number
            friend: number
            attention: number
            sign: string
            level_info: {
              current_level: number
              current_min: number
              current_exp: number
              next_exp: number
            }
            pendant: {
              pid: number
              name: string
              image: string
              expire: number
              image_enhance: string
              image_enhance_frame: string
              n_pid: number
            }
            nameplate: {
              nid: number
              name: string
              image: string
              image_small: string
              level: string
              condition: string
            }
            Official: {
              role: number
              title: string
              desc: string
              type: number
            }
            official_verify: {
              type: number
              desc: string
            }

            vip: {
              type: number
              status: number
              due_date: number
              vip_pay_type: number
              theme_type: number
              label: {
                path: string
                text: string
                label_theme: string
                text_color: string
                bg_style: number
                bg_color: string
                border_color: string
                use_img_label: boolean
                img_label_uri_hans: string
                img_label_uri_hant: string
                img_label_uri_hans_static: string
                img_label_uri_hant_static: string
              }
              avatar_subscript: number
              nickname_color: string
              role: number
              avatar_subscript_url: string
              tv_vip_status: number
              tv_vip_pay_type: number
              tv_due_date: number
              avatar_icon: {
                icon_type: number
                icon_resource: {}
              }
              vipType: number
              vipStatus: number
            }
            is_senior_member: number
            name_render: null
          }
          following: boolean
          archive_count: number
          article_count: number
          follower: number
          like_num: number
        }
      }

      /**
       * 获取用户基本信息，主要用来获取头像，官方用途是用来获取用户鼠标移动到粉丝勋章上之后获取粉丝勋章对应的主播信息
       *
       * 请注意，此 API 不会判断用户是否存在，传入任意 uid 依然会返回对应的 ruid，其他字段全部为空😅
       *
       * 自 Apr 25, 2024 起，该接口提高风控，会检测 CF Workers 的 CF- 头
       *
       * @link https://api.live.bilibili.com/xlive/web-room/v1/index/getDanmuMedalAnchorInfo?ruid=${uid}
       */
      export interface GetDanmuMedalAnchorInfo {
        code: number
        message: string
        ttl: number
        data: {
          ruid: number
          runame: string
          rface: string
          fans_club_count: number
          live_stream_status: number
          anchor_roomid: number
        }
      }

      /**
       * 用户关注，来自游戏中心
       * @link https://line3-h5-mobile-api.biligame.com/game/center/h5/user/relationship/following_list?vmid=${uid}&re_version=&pn=${page}&ps=50&order=desc&device_id=&build=&mid=&source_from=0&from_game=0&cur_host=app(H5-Android)&client=h5&sdk_type=1&cur_mid=${requesterUid}
       */
      export interface GameCenterFollowingList {
        code: number
        data: {
          list: GameCenterFollowingListDataListItem[]
        }
        /**
         * 请求时间戳
         */
        ts: number
        /**
         * 请求识别 ID，需要避免暴露给前端，以防被追踪
         */
        request_id?: string
      }

      /**
       * 关注项目
       */
      export interface GameCenterFollowingListDataListItem {
        mid: string
        /**
         * 关注类型，
         * - 2: 单方面关注
         * - 6: 互关
         */
        attribute: number
        uname: string
        face: string
        /** 认证信息 */
        attestation_display: {
          /**
           * - 0: 无认证
           * - 2: 黄v
           * - 3: 蓝v
           */
          type: number
          desc: string
        }
      }

      /**
       * 直播个人信息，只有登录的时候可用
       * @link https://api.live.bilibili.com/xlive/web-ucenter/user/get_user_info
       */
      export interface LiveGetUserInfo {
        /**
         * - `0`: 登录状态
         * - `-101`: 未登录
         */
        code: number
        message: string
        ttl: number
        data?: LiveGetUserInfoData
      }

      /** Data of `LiveGetUserInfo` */
      export interface LiveGetUserInfoData {
        uid: number
        uname: string
        face: string
        billCoin: number
        silver: number
        gold: number
        achieve: number
        vip: number
        svip: number
        user_level: number
        user_next_level: number
        user_intimacy: number
        user_next_intimacy: number
        is_level_top: number
        user_level_rank: string
        user_charged: number
        identification: number
        wealth_info: {
          uid: number
          level: number
          level_total_score: number
          cur_score: number
          upgrade_need_score: number
          status: number
          dm_icon_key: string
        }
      }

      /**
       * 获取当前房间的礼物配置，访问礼物面板时触发
       * @link https://api.live.bilibili.com/xlive/web-room/v1/giftPanel/roomGiftConfig?platform=pc&room_id=25034104
       * @link https://api.live.bilibili.com/xlive/web-room/v1/giftPanel/roomGiftConfig?platform=pc&room_id=${room_id}&area_parent_id=9&area_id=371&source=live&build=0&global_version=${now}
       * @example /bilibili/room-gift-config/25034104?list=1
       * @deprecated Mar 26, 2025 今天发现b站现在已经不再调用此 api，更改为下方的 RoomGiftList
       */
      export interface RoomGiftConfig {
        code: number
        message: string
        /**
         * Usually `1`
         */
        ttl: number
        data: {
          global_gift: {
            hited: boolean
            /**
             * 传入特定参数后可能为空
             */
            list: RoomGiftItem[] | null
            version: number
            ttl: number
          }
          list: RoomGiftItem[]
          combo_resources: {
            combo_resources_id: number
            img_one: string
            img_two: string
            img_three: string
            img_four: string
            color_one: string
            color_two: string
          }[]
          guard_resources: {
            level: number
            img: string
            name: string
          }[]
          naming_gift: {
            text: {
              // 由你冠名
              app_user: string
              app_user_selected: string
              web_user: string
              web_user_selected: string
              // 由TA冠名
              combo_user: string
              combo_anchor: string
              // TA冠名的礼物
              vtr: string
            }
          }
          send_disable_msg: {
            // 只能给房主投喂～
            gift_for_owner: string
            // 请选择投喂对象～
            no_send_obj: string
            // 投喂给房主的礼物才可增加亲密度哦，<%查看详情>%>
            no_fans_incr: string
            jump_fans_url: string
            // 投喂给房主的礼物才可增加亲密度哦
            web_no_fans_incr: string
          }
        }
      }

      /**
       * 获取当前房间的礼物配置，访问礼物面板时触发
       *
       * - 完整的请求需要传递 w_rid
       * - base_version 不为 0 时不会返回 gift list，可能是为了省那几十 KB 的宽带叭😅
       *
       * @link https://api.live.bilibili.com/xlive/web-room/v1/giftPanel/roomGiftList?platform=pc&room_id=12312313&area_parent_id=9&area_id=744&source=live&build=0&ruid=123123141231&base_version=0&receive_users=&web_location=444.8&w_rid=qweqweqweqewqweqweqewqweqwe&wts=1743015811
       * @example /bilibili/live-gift/25034104?list=1
       */
      export interface RoomGiftList {
        code: number
        message: string
        ttl: number
        data: {
          gift_data: {
            room_gift_list: {
              gold_list: RoomGiftListItem[]
              silver_list: any[]
              need_odds_offline: boolean
              ab_result: {
                gift_panel_algorithm_v4: string
              }
            }
            discount_gift_list: null
            tab_list: {
              tab_id: number
              tab_name: string
              position: number
              list: RoomGiftListItem[]
            }[]
            max_send_gift: number
            combo_interval_time: number
            lottery_gift_config: null
            privilege: {
              buy_guard_btn: string
              is_expired: number
              privilege_type: number
            }
            red_dot: any[]
            pay_limit_icon: string
            naming_gift: null
            special_show_gift: null
            bag_tab_disable: number
            special_tag: any[]
          }
          gift_config: {
            base_config: {
              hited: boolean
              list: RoomGiftItem[]
              /** 1742991904002, 版本时间戳 */
              version: number
              /** 1743059283281, 当前时间戳？ */
              ttl: number
            }
            /** 通常为总督一号、提督一号这些 romm specific 的玩意 */
            room_config: RoomGiftItem[]
          }
          global_config: {
            combo_resources: {
              combo_resources_id: number
              img_one: string
              img_two: string
              img_three: string
              img_four: string
              color_one: string
              color_two: string
            }[]
            guard_resources: {
              level: number
              img: string
              name: string
            }[]
            naming_gift: {
              text: {
                app_user: string
                app_user_selected: string
                web_user: string
                web_user_selected: string
                combo_user: string
                combo_anchor: string
                vtr: string
              }
            }
            send_disable_msg: {
              gift_for_owner: string
              no_send_obj: string
              no_fans_incr: string
              jump_fans_url: string
              web_no_fans_incr: string
            }
            gift_protocols: {
              gift_ids: number[]
              protocols: {
                text: string
                url: string
              }[]
            }[]
          }
        }
      }

      /**
       * List Item of `RoomGiftList`
       *
       * 不包含礼物名称的一些礼物补充信息，例如出现位置？赠送方式等，暂时没啥用
       */
      export interface RoomGiftListItem {
        position: number
        gift_id: number
        id: number
        plan_id: number
        special: {
          special_type: number
          is_use: number
          /** 开通大航海就能送辣(*╹▽╹*)快加入主播舰队吧 */
          tips: string
          url: string
        }
        upgrade_gift:
          | {
              gift_id: number
              alias: string
              /** +0电池 */
              desc: string
              locked: boolean
              lock_tip: string
            }[]
          | null
        gift_tag: number[] | null
        extra_info: {
          is_fixed: number
          icon_bottom_tips: string
          alg: string
          gift_sort_strategy: number
        }
        gift_scene: {
          scene: 'comm_blind_box' | 'default_gift' | 'guard_attire' | 'guard_gift' | 'traffic_gift'
          pay_type: 'interactive_pay' | 'send_gift'
          hide_price: boolean
        } | null
      }

      /** Item of `RoomGiftConfig` and `RoomGiftList` */
      export interface RoomGiftItem {
        id: number
        name: string
        price: number
        type: number
        coin_type: 'gold' | 'silver'
        bag_gift: number
        effect: number
        corner_mark: string
        corner_background: string
        broadcast: number
        draw: number
        stay_time: number
        animation_frame_num: number
        desc: string
        rule: string
        rights: string
        privilege_required: number
        count_map: {
          num: number
          text: string
          desc: string
          web_svga: string
          vertical_svga: string
          horizontal_svga: string
          special_color: string
          effect_id: number
        }[]
        img_basic: string
        img_dynamic: string
        frame_animation: string
        gif: string
        webp: string
        full_sc_web: string
        full_sc_horizontal: string
        full_sc_vertical: string
        full_sc_horizontal_svga: string
        full_sc_vertical_svga: string
        bullet_head: string
        bullet_tail: string
        limit_interval: number
        bind_ruid: number
        bind_roomid: number
        gift_type: number
        combo_resources_id: number
        max_send_limit: number
        weight: number
        goods_id: number
        has_imaged_gift: number
        left_corner_text: string
        left_corner_background: string
        gift_banner: null | {
          app_pic: string
          web_pic: string
          left_text: string
          left_color: string
          button_text: string
          button_color: string
          button_pic_color: string
          jump_url: string
          jump_to: number
          web_pic_url: string
          web_jump_url: string
        }
        diy_count_map: number
        effect_id: number
        first_tips: string
        gift_attrs: number[]
        corner_mark_color: string
        corner_color_bg: string
        web_light: {
          corner_mark: string
          corner_background: string
          corner_mark_color: string
          corner_color_bg: string
        }
        web_dark: {
          corner_mark: string
          corner_background: string
          corner_mark_color: string
          corner_color_bg: string
        }
      }

      /**
       * 筛选后的礼物，用于简化输出
       */
      export type RoomGiftItemPick = Pick<
        RoomGiftItem,
        'id' | 'name' | 'webp' | 'price' | 'coin_type' | 'desc' | 'rule' | 'rights' | 'corner_mark'
      > & {
        global: boolean
      }

      /**
       * @example /bilibili/room-gift-config
       */
      export interface RoomGiftConfigOutput {
        code: number
        message: string
        ttl: number
        gift_ttl: number
        gift_version: number
        data: {
          list: RoomGiftItemPick[]
        }
      }

      /**
       * 获取礼物特效配置
       * `base_version` 为 0 时可获得所有特效
       * @example https://api.live.bilibili.com/xlive/general-interface/v1/fullScSpecialEffect/GetEffectConfListV2?platform=pc&room_id=134123123&area_parent_id=9&area_id=744&source=live&build=0&base_version=0&web_location=444.8&w_rid=1qwe1r123e123123&wts=12313123123
       */
      export interface GetEffectConfListV2 {
        code: number
        message: string
        ttl: number
        data: {
          full_sc_resource: {
            conf_list: GiftEffectItem[]
            base_version: number
            ttl: number
          }
          float_sc_resource: {
            title: string
            type: number
            left_color: string
            right_color: string
            face_background: string
            tail_background: string
            id: number
          }[]
        }
      }

      /** Item of `FullScResource` */
      export interface GiftEffectItem {
        /**
         * 特效类型
         *
         * - 1 礼物特效
         * - 2 指挥官特效、守护进场特效、开盲盒特效
         * - 3 总在舰榜（1、2、3）特效
         * - 4 未知
         * - 5 各种进场特效
         */
        type: number
        web_svga: string
        horizontal_svga: string
        vertical_svga: string
        /**
         * @example https://i0.hdslb.com/bfs/live/cf12deb852532be8d15549109c59d407a85c0e54.mp4
         */
        web_mp4: string
        web_mp4_json: string
        /**
         * @example https://i0.hdslb.com/bfs/live/cf12deb852532be8d15549109c59d407a85c0e54.mp4
         */
        horizontal_mp4: string
        /**
         * @example https://i0.hdslb.com/bfs/live/cf12deb852532be8d15549109c59d407a85c0e54.mp4
         */
        vertical_mp4: string
        id: number
        plan_platform: number[]
        /**
         * 特效所绑定的对应礼物 id
         */
        bind_gift_ids: number[]
        web_mp4_md5: string
        horizontal_mp4_md5: string
        vertical_mp4_md5: string
        web_mp4_crc32: number
        horizontal_mp4_crc32: number
        vertical_mp4_crc32: number
        web_mp4_file_size: number
        horizontal_mp4_file_size: number
        vertical_mp4_file_size: number
        h265_conf: {
          horizontal_mp4: {
            mp4: string
            mp4_md5: string
            mp4_json: string
            mp4_crc32: number
            mp4_file_size: number
          }
          vertical_mp4: {
            mp4: string
            mp4_md5: string
            mp4_json: string
            mp4_crc32: number
            mp4_file_size: number
          }
        }
      }

      /**
       * 礼物特效配置
       *
       * 用于渲染礼物视频的配置
       *
       * @example https://i0.hdslb.com/bfs/live/d9be47f7bb6127336d22e8135fb61e95736f69a0.json
       */
      export interface GiftEffectItemConfig {
        info: {
          aFrame: number[]
          align: number
          avc_crf: number
          codec: string
          custom: number
          f: number
          fps: number
          h: number
          hevc_crf: number
          rgbFrame: number[]
          scale: number
          tool_version: string
          v: number
          videoH: number
          videoW: number
          w: number
        }
      }

      /**
       * 指定用户粉丝勋章
       * /bilibili/user-medals
       */
      export interface MedalWall {
        /**
         * 0 正常
         */
        code: number
        /**
         * "0"
         */
        message: string
        /**
         * 通常为 1
         */
        ttl: number
        /**
         * 未登录或报错时此字段为空
         */
        data?: MedalWallData
      }

      /** Data of `MedalWall` */
      export interface MedalWallData {
        list: MedalWallListItem[]
        /**
         * 粉丝勋章总数
         */
        count: number
        close_space_medal: number
        /**
         * 只显示正在佩戴
         */
        only_show_wearing: number
        /**
         * 当前用户名称
         */
        name: string
        /**
         * 当前用户头像
         */
        icon: string
        /**
         * 当前请求 API 的用户 UID，为了保障隐私，workers 处理时会去掉此字段，因此标记为 optional
         */
        uid?: number
        /**
         * 当前请求 API 的用户b站等级，为了保障隐私，workers 处理时会去掉此字段，因此标记为 optional
         */
        level?: number
      }

      /** Item of `MedalWallData` */
      export interface MedalWallListItem {
        medal_info: MedalWallListItemMedalInfo
        target_name: string
        target_icon: string
        link: string
        live_status: number
        official: number
      }

      /** Props of `MedalWallListItemMedalInfo` */
      export interface MedalWallListItemMedalInfo {
        target_id: number
        level: number
        medal_name: string
        medal_color_start: number
        medal_color_end: number
        medal_color_border: number
        guard_level: number
        wearing_status: number
        medal_id: number
        intimacy: number
        next_intimacy: number
        today_feed: number
        day_limit: number
        guard_icon: string
        honor_icon: string
      }

      /**
       * 充电排行榜
       * /bilibili/upower
       */
      export interface Upower {
        code: number
        message: string
        ttl: number
        data: UpowerData
      }

      /** Data of `Upower` */
      export interface UpowerData {
        up_info: {
          mid: number
          nickname: string
          avatar: string
          type: number
          title: string
          upower_state: number
        }
        rank_info: UpowerItem[]
        /**
         * 当前请求 API 的用户信息，为了保障隐私，workers 处理时会去掉此字段，因此标记为 optional
         */
        user_info?: UpowerItem
        member_total: number
        privilege_type: number
        is_charge: boolean
        tabs: number[]
        level_info: {
          privilege_type: number
          name: string
          price: number
          member_total: number
        }[]
      }

      /** Item of `UpowerData` */
      export interface UpowerItem {
        mid: number
        nickname: string
        avatar: string
        rank: number
        day: number
        expire_at: number
        remain_days: number
      }

      /**
       * 大航海列表
       * /bilibili/live-guards
       * @link https://api.live.bilibili.com/xlive/app-room/v2/guardTab/topList?roomid=1&page=${page}&ruid=${uid}&page_size=30
       */
      export interface LiveGuards {
        code: number
        message: string
        ttl: number
        data: LiveGuardsData
      }

      /** Data of `LiveGuards` */
      export interface LiveGuardsData {
        info: {
          num: number
          page: number
          now: number
          achievement_level: number
          anchor_guard_achieve_level: number
        }
        list: LiveGuardsListItem[]
        top3: LiveGuardsListItem[]
        /**
         * 当前请求 API 的用户信息，为了保障隐私，workers 处理时会去掉此字段，因此标记为 optional
         */
        my_follow_info?: {
          guard_level: number
          accompany_days: number
          expired_time: Date
          auto_renew: number
          renew_remind: {
            content: string
            type: number
            hint: string
          }
          medal_info: LiveGuardsListItemMedalInfo
          rank: number
          ruid: number
          face: string
        }
        /**
         * 当前请求 API 的用户的大航海到期信息，为了保障隐私，workers 处理时会去掉此字段，因此标记为 optional
         */
        guard_warn?: {
          is_warn: number
          warn: string
          expired: number
          will_expired: number
          address: string
        }
        exist_benefit: boolean
        remind_benefit: string
        ab: {
          guard_accompany_list: number
        }
        remind_msg: string
        typ: number
      }

      /** Item of `LiveGuardsData` */
      export interface LiveGuardsListItem {
        uid: number
        ruid: number
        rank: number
        username: string
        face: string
        is_alive: number
        guard_level: number
        guard_sub_level: number
        medal_info: LiveGuardsListItemMedalInfo
        accompany: number
      }

      /** Medal info of live guard item */
      export interface LiveGuardsListItemMedalInfo {
        medal_name: string
        medal_level: number
        medal_color_start: number
        medal_color_end: number
        medal_color_border: number
      }

      /**
       * 大航海列表，新版，包含陪伴时长排序，但是不但要求房间号，还要求传入 UID
       * /bilibili/live-guards
       * @link https://api.live.bilibili.com/xlive/app-room/v2/guardTab/topListNew?roomid=21013446&page=1&ruid=*********&page_size=20&typ=0&platform=web
       */
      export interface LiveGuardsV2 {
        code: number
        message: string
        ttl: number
        data: LiveGuardsV2Data
      }

      /** Data of LiveGuardsV2 */
      export interface LiveGuardsV2Data {
        info: {
          num: number
          page: number
          now: number
          achievement_level: number
          anchor_guard_achieve_level: number
          achievement_icon_src: string
          buy_guard_icon_src: string
          rule_doc_src: string
          ex_background_src: string
          color_start: string
          color_end: string
          tab_color: string[]
          title_color: string[]
        }
        list: LiveGuardsV2List[]
        top3: LiveGuardsV2List[]
        my_follow_info: {
          accompany_days: number
          auto_renew: number
          renew_remind: {
            content: string
            type: number
            hint: string
          }
          rank: number
          ruid: number
          uinfo: LiveGuardsV2Uinfo
          expired_time: string
        }
        guard_warn: {
          is_warn: number
          warn: string
          expired: number
          will_expired: number
          address: string
        }
        exist_benefit: boolean
        remind_benefit: string
        ab: {
          guard_accompany_list: number
        }
        remind_msg: string
        typ: number
        extop: null
        guard_leader: null
      }

      /** List of LiveGuardsV2 */
      export interface LiveGuardsV2List {
        ruid: number
        rank: number
        accompany: number
        uinfo: LiveGuardsV2Uinfo
      }

      /** Uinfo of LiveGuardsV2 */
      export interface LiveGuardsV2Uinfo {
        uid: number
        base: {
          name: string
          face: string
          name_color: number
          is_mystery: boolean
          risk_ctrl_info: null
          origin_info: {
            name: string
            face: string
          }
          official_info: {
            role: number
            title: string
            desc: string
            type: number
          }
          name_color_str: string
        }
        medal: MedalInfo
        wealth: null
        title: null
        guard: {
          level: number
          expired_str: string
        }
        uhead_frame: null
        guard_leader: null
      }

      /**
       * 粉丝团列表
       */
      export interface LiveFansMembers {
        code: number
        message: string
        ttl: number
        data: LiveFansMembersData
      }

      /** Data of `LiveFansMembers` */
      export interface LiveFansMembersData {
        item?: LiveFansMembersItem[]
        num: number
        medal_status: number
      }

      /** Item of `LiveFansMembersData` */
      export interface LiveFansMembersItem {
        user_rank: number
        uid: number
        name: string
        face: string
        score: number
        medal_name: string
        level: number
        target_id: number
        special: string
        guard_level: number
        medal_color_start: number
        medal_color_end: number
        medal_color_border: number
        guard_icon: string
        honor_icon: string
      }

      /**
       * 泄漏的主播工会 API，目前已修复
       */
      export interface LiveGuildBlsSummer2023 {
        _ts_rpc_return_: {
          code: number
          message: string
          data: LiveGuildBlsSummer2023Data
          errors: {}
        }
      }

      /** Data of `LiveGuildBlsSummer2023` */
      export interface LiveGuildBlsSummer2023Data {
        anchorInfo: {
          settlement: number
          params: {
            rank_id: number
            have_diff: number
            diff_spec: number
            dimension_other: number
            dimension_timestamp: number
          }[]
        }
        hourlyLottery: Array<any[]>
        /**
         * 泄漏的工会字段，目前文本全部返回为空，整数返回为 0
         */
        guildInfo: LiveGuildBlsSummer2023GuildItem
        time: number
        anchor: {
          name: string
          face: string
        }
        stage: number
        actStatus: number
        stageName: string
        tabJson: {
          idolRank: {
            id: number
          }
          pkStarRank: {
            id: number
          }
          singleRank1: {
            tabName: string
            tabId: number
            params: {
              teamDimensionValue: number
              id: number
            }
          }[]
          singleRank2: {
            tabName: string
            params1: {
              tabId: number
              teamDimensionValue: number
              id: number
            }
            params2: {
              tabId: number
              teamDimensionValue: number
              id: number
            }
          }[]
          hourRank: {
            id: number
          }
          awardsTabs: {
            tabName: string
            awardId: number
            tabId: number
          }[]
        }
        teamId: number
        teamName: string
      }

      /** Item of `LiveGuildBlsSummer2023Data` */
      export interface LiveGuildBlsSummer2023GuildItem {
        id: number
        name: string
        /**
         * 标为可选，workers 返回时会去掉此字段用于节省带宽
         */
        logo?: string
        team: number
        rank: number
        score: number
        /**
         * workers 额外增加的字段，为了简单处理，直接加在原始 type 上
         */
        lastModified?: number
      }

      /**
       * GetRoomPlayInfo
       * 获取直播视频流信息，也可用于根据直播间房间号获取 UID
       *
       * May 30, 2025, 1:24:24 AM PDT 发现该接口也需要 web_location 封控
       *
       * @deprecated May 30, 2025, 1:31:07 AM PDT 该请求与 getInfoByRoom 一样，在普通直播间也已经不再使用，会直接嵌入 DOM
       * 通过 /blanc/21696950 可暂时强制恢复该请求调用
       *
       * @link https://api.live.bilibili.com/xlive/web-room/v2/index/getRoomPlayInfo?room_id=1182221&protocol=0&format=0,1,2&codec=0&qn=10000&platform=web&ptype=8&dolby=5&panorama=1
       * @link https://api.live.bilibili.com/xlive/web-room/v2/index/getRoomPlayInfo?room_id=31361500&protocol=0%2C1&format=0%2C1%2C2&codec=0%2C1%2C2&qn=10000&platform=web&ptype=8&dolby=5&panorama=1&hdr_type=0%2C1&req_reason=1&web_location=444.8&w_rid=55da3f4eb943b742d067dcc3d3616567&wts=1748589841
       */
      export interface GetRoomPlayInfo {
        code: number
        message: string
        ttl: number
        data: GetRoomPlayInfoData
      }

      /** Data of `GetRoomPlayInfo` */
      export interface GetRoomPlayInfoData {
        room_id: number
        /** 短房间号，如果没有则返回 0 */
        short_id: number
        uid: number
        is_hidden: boolean
        is_locked: boolean
        is_portrait: boolean
        /**
         * - 0: 未开播
         * - 1: 直播中
         * - 2: 轮播中
         */
        live_status: number
        /** 隐藏？ */
        hidden_till: number
        /** 封禁相关 */
        lock_till: number
        /** 加密房间 */
        encrypted: boolean
        pwd_verified: boolean
        /** 直播开始时间戳 */
        live_time: number
        room_shield: number
        all_special_types: number[]
        playurl_info: PlayUrlInfoItem | null
        /** 通常为 0 */
        official_type: number
        /** 通常为 0 */
        official_room_id: number
        /** 风控延迟？通常为 0 */
        risk_with_delay: number
      }

      /** Item of `playurl_info` in `GetRoomPlayInfoData` */
      export interface PlayUrlInfoItem {
        conf_json: string
        playurl: {
          cid: number
          g_qn_desc: {
            /**
             * 画质
             * - 80 流畅
             * - 150 高清
             * - 250 超清
             * - 400 蓝光
             * - 10000 原画
             * - 20000 4K
             * - 30000 杜比
             */
            qn: number
            /** 上方的描述文本 */
            desc: string
            hdr_desc: string
            attr_desc: null
          }[]
          stream: {
            /** ie. `http_stream` */
            protocol_name: string
            format: {
              /** ie. `flv` */
              format_name: string
              codec: {
                /** ie. `avc` */
                codec_name: string
                current_qn: number
                accept_qn: number[]
                /** ie. `/live-bvc/768929/live_835065_8611103.flv?` */
                base_url: string
                url_info: {
                  /** `https://cn-hnld-ct-01-32.bilivideo.com` */
                  host: string
                  /** `expires=1719427019&pt=web&deadline=1719427019&len=0&oi=0x240e035f0b0b4b00757bfdd88d3ffd6d....` */
                  extra: string
                  stream_ttl: number
                }[]
                hdr_qn: null
                dolby_type: number
                attr_name: string
              }[]
              /** 通常为空 */
              master_url: string
            }[]
          }[]
          p2p_data: {
            p2p: boolean
            p2p_type: number
            m_p2p: boolean
            m_servers: null
          }
          dolby_qn: null
        }
      }

      /**
       * 荣耀等级配置
       */
      export interface WealthConfig {
        code: number
        message: string
        ttl: number
        data: {
          md5: string
          /** stringify 后的 JSON，见下方 */
          content: string | WealthConfigContent
        }
      }

      /** Props of `WealthConfig` */
      export interface WealthConfigContent {
        /**
         * 荣耀等级长条勋章 badge 配置
         */
        wealth_level_medal: WealthLevelMedal[]
        /**
         * 荣耀等级介绍页面 URL
         */
        wealth_level_url: string
        /**
         * 荣耀等级图标（不包含等级）
         */
        player_icon: WealthLevelIcon[]
        /**
         * 聊天气泡配置
         */
        danmu_bubble_bg: WealthLevelDanmuBubbleBg[]
      }

      /** Props of `WealthConfigContent` */
      export interface WealthLevelDanmuBubbleBg {
        id: number
        title: string
        business_type: number
        /**
         * 后台发奖, 舰队指挥官, 财富等级
         */
        business_desc: string
        reward_type: number
        web: WealthLevelDanmuBubbleBgConfig
        mobile: WealthLevelDanmuBubbleBgConfig
        pc_dark: WealthLevelDanmuBubbleBgConfig
        pc_light: WealthLevelDanmuBubbleBgConfig
        preview: WealthLevelDanmuBubbleBgConfig
        upper_preview_url: string
      }

      /** Props of `WealthLevelDanmuBubbleBg` */
      export interface WealthLevelDanmuBubbleBgConfig {
        url: string
        animated: number
        x: number
        y: number
        width: number
        height: number
        first_line_bkg_url: string
      }

      /** Props of `WealthConfigContent` */
      export interface WealthLevelIcon {
        /**
         * 从 40 级开始有图标，这里又变成字符串了 😅，此处最低为 "40"
         */
        id: string
        key: string
        width: number
        height: number
        url: string
      }

      /** Props of `WealthConfigContent` */
      export interface WealthLevelMedal {
        /**
         * 荣耀等级，从 1 开始
         */
        id: number
        animated: number
        url: string
        h: number
        w: number
      }

      /**
       * 用于获取用户 IP
       */
      export interface Zone {
        code: number
        message: string
        ttl: number
        data: {
          addr: string
          country: string
          province: string
          isp: string
          latitude: number
          longitude: number
          zone_id: number
          country_code: number
        }
      }

      /**
       * 充电排行榜
       * @link https://api.bilibili.com/x/ugcpay-rank/elec/month/up?up_mid=2132180406
       * @deprecated 已被 Upower 代替
       */
      export interface UgcPay {
        code: number
        message: string
        ttl: number
        data: {
          count: number
          list: {
            uname: string
            avatar: string
            mid: number
            pay_mid: number
            rank: number
            trend_type: number
            vip_info: {
              vipDueMsec: number
              vipStatus: number
              vipType: number
            }
            message: string
            message_hidden: number
          }[]
          total_count: number
          total: number
          special_day: number
        }
      }

      /**
       * 个人空间首页信息，中高风控
       * @link https://api.bilibili.com/x/space/wbi/acc/info?mid=2763&token=&platform=web&web_location=1550101&dm_img_list=[]&dm_img_str=V2ViR0wgMS4wIChPcGVuR0wgRVMgMi4wIENocm9taXVtKQ&dm_cover_img_str=QU5HTEUgKEFwcGxlLCBBTkdMRSBNZXRhbCBSZW5kZXJlcjogQXBwbGUgTTIgTWF4LCBVbnNwZWNpZmllZCBWZXJzaW9uKUdvb2dsZSBJbmMuIChBcHBsZS&dm_img_inter=%7B%22ds%22:[],%22wh%22:[4148,6321,28],%22of%22:[140,280,140]%7D&w_rid=c4913a3b0cc9d0a9dc502c3517be1e55&wts=1713897628
       */
      export interface UserSpace {
        code: number
        message: string
        ttl: number
        data: UserSpaceData
      }

      /** Data of `UserSpace` */
      export interface UserSpaceData {
        mid: number
        name: string
        sex: string
        face: string
        face_nft: number
        face_nft_type: number
        sign: string
        rank: number
        level: number
        jointime: number
        moral: number
        silence: number
        coins: number
        fans_badge: boolean
        fans_medal: {
          show: boolean
          wear: boolean
          medal: null
        }
        official: {
          role: number
          title: string
          desc: string
          type: number
        }
        vip: {
          type: number
          status: number
          due_date: number
          vip_pay_type: number
          theme_type: number
          label: {
            path: string
            text: string
            label_theme: string
            text_color: string
            bg_style: number
            bg_color: string
            border_color: string
            use_img_label: boolean
            img_label_uri_hans: string
            img_label_uri_hant: string
            img_label_uri_hans_static: string
            img_label_uri_hant_static: string
          }
          avatar_subscript: number
          nickname_color: string
          role: number
          avatar_subscript_url: string
          tv_vip_status: number
          tv_vip_pay_type: number
          tv_due_date: number
          avatar_icon: {
            icon_type: number
            icon_resource: {}
          }
        }
        pendant: {
          pid: number
          name: string
          image: string
          expire: number
          image_enhance: string
          image_enhance_frame: string
          n_pid: number
        }
        nameplate: {
          nid: number
          name: string
          image: string
          image_small: string
          level: string
          condition: string
        }
        user_honour_info: {
          mid: number
          colour: null
          tags: any[]
          is_latest_100honour: number
        }
        is_followed: boolean
        /**
         * @example `bfs/space/dbba57ab872ccd22940d8c812a22655c309c852f.png`
         */
        top_photo: string
        /**
         * @since 2025-06-18
         */
        top_photo_v2: {
          l_200h_img: string
          l_img: string
          sid: number
        }
        theme: {}
        sys_notice: {}
        live_room: {
          roomStatus: number
          liveStatus: number
          url: string
          title: string
          cover: string
          roomid: number
          roundStatus: number
          broadcast_type: number
          watched_show: {
            switch: boolean
            num: number
            text_small: string
            text_large: string
            icon: string
            icon_location: string
            icon_web: string
          }
        }
        birthday: string
        school: {
          name: string
        }
        profession: {
          name: string
          department: string
          title: string
          is_show: number
        }
        tags: null
        series: {
          user_upgrade_status: number
          show_upgrade_window: boolean
        }
        is_senior_member: number
        mcn_info: null
        gaia_res_type: number
        gaia_data: null
        is_risk: boolean
        elec: {
          show_info: {
            show: boolean
            state: number
            title: string
            icon: string
            jump_url: string
          }
        }
        contract: {
          is_display: boolean
          is_follow_display: boolean
        }
        certificate_show: boolean
      }

      /**
       * 个人空间首页动态列表，高风控
       * @link https://api.bilibili.com/x/polymer/web-dynamic/v1/feed/space?offset=&host_mid=2763&timezone_offset=420&platform=web&features=itemOpusStyle,listOnlyfans,opusBigCover,onlyfansVote&web_location=333.999&dm_img_list=[%7B%22x%22:2493,%22y%22:633,%22z%22:0,%22timestamp%22:5738,%22k%22:95,%22type%22:0%7D,%7B%22x%22:1875,%22y%22:1922,%22z%22:101,%22timestamp%22:5838,%22k%22:80,%22type%22:0%7D,%7B%22x%22:1506,%22y%22:1929,%22z%22:214,%22timestamp%22:464651,%22k%22:123,%22type%22:0%7D,%7B%22x%22:1578,%22y%22:2011,%22z%22:256,%22timestamp%22:465561,%22k%22:81,%22type%22:0%7D,%7B%22x%22:1817,%22y%22:2361,%22z%22:24,%22timestamp%22:465661,%22k%22:119,%22type%22:0%7D,%7B%22x%22:1562,%22y%22:1876,%22z%22:22,%22timestamp%22:465762,%22k%22:114,%22type%22:0%7D,%7B%22x%22:1513,%22y%22:-345,%22z%22:348,%22timestamp%22:465863,%22k%22:61,%22type%22:0%7D,%7B%22x%22:1444,%22y%22:-785,%22z%22:288,%22timestamp%22:465964,%22k%22:114,%22type%22:0%7D,%7B%22x%22:1253,%22y%22:-1147,%22z%22:58,%22timestamp%22:466065,%22k%22:101,%22type%22:0%7D,%7B%22x%22:1563,%22y%22:-760,%22z%22:160,%22timestamp%22:468941,%22k%22:72,%22type%22:0%7D,%7B%22x%22:2448,%22y%22:415,%22z%22:1026,%22timestamp%22:469041,%22k%22:107,%22type%22:0%7D,%7B%22x%22:2328,%22y%22:487,%22z%22:905,%22timestamp%22:469142,%22k%22:64,%22type%22:0%7D,%7B%22x%22:1433,%22y%22:-398,%22z%22:3,%22timestamp%22:469242,%22k%22:86,%22type%22:0%7D,%7B%22x%22:1639,%22y%22:-289,%22z%22:224,%22timestamp%22:469343,%22k%22:66,%22type%22:0%7D,%7B%22x%22:1581,%22y%22:-355,%22z%22:167,%22timestamp%22:469444,%22k%22:61,%22type%22:0%7D,%7B%22x%22:2742,%22y%22:810,%22z%22:1316,%22timestamp%22:470260,%22k%22:102,%22type%22:0%7D,%7B%22x%22:1461,%22y%22:-464,%22z%22:14,%22timestamp%22:470361,%22k%22:94,%22type%22:0%7D,%7B%22x%22:2419,%22y%22:689,%22z%22:962,%22timestamp%22:470472,%22k%22:102,%22type%22:0%7D,%7B%22x%22:1679,%22y%22:64,%22z%22:199,%22timestamp%22:470572,%22k%22:104,%22type%22:0%7D,%7B%22x%22:1656,%22y%22:57,%22z%22:174,%22timestamp%22:470673,%22k%22:87,%22type%22:0%7D,%7B%22x%22:2180,%22y%22:581,%22z%22:698,%22timestamp%22:470775,%22k%22:99,%22type%22:1%7D,%7B%22x%22:1702,%22y%22:103,%22z%22:220,%22timestamp%22:470877,%22k%22:76,%22type%22:0%7D]&dm_img_str=V2ViR0wgMS4wIChPcGVuR0wgRVMgMi4wIENocm9taXVtKQ&dm_cover_img_str=QU5HTEUgKEFwcGxlLCBBTkdMRSBNZXRhbCBSZW5kZXJlcjogQXBwbGUgTTIgTWF4LCBVbnNwZWNpZmllZCBWZXJzaW9uKUdvb2dsZSBJbmMuIChBcHBsZS&dm_img_inter=%7B%22ds%22:[%7B%22t%22:4,%22c%22:%22bi1idG4gbi1keW5hbWljIHJvdXRlci1saW5rLWV4YWN0LWFjdGl2ZSByb3V0ZXItbGluay1hY3RpdmUgYWN0aX%22,%22p%22:[1302,74,774],%22s%22:[60,342,56]%7D],%22wh%22:[4235,6350,57],%22of%22:[124,248,124]%7D&x-bili-device-req-json=%7B%22platform%22:%22web%22,%22device%22:%22pc%22%7D&x-bili-web-req-json=%7B%22spm_id%22:%22333.999%22%7D&w_rid=48f5428e6afc1934bafa4a6c4a1d1755&wts=1713898099
       * Sep 16, 2024, 10:26 PM:
       * @link https://api.bilibili.com/x/polymer/web-dynamic/v1/feed/space?offset=&host_mid=2763&timezone_offset=-480&platform=web&features=itemOpusStyle,listOnlyfans,opusBigCover,onlyfansVote,decorationCard,forwardListHidden,ugcDelete,onlyfansQaCard&web_location=333.999&dm_img_list=[%7B%22x%22:2709,%22y%22:-1172,%22z%22:0,%22timestamp%22:5,%22k%22:68,%22type%22:0%7D,%7B%22x%22:2242,%22y%22:-1224,%22z%22:36,%22timestamp%22:128,%22k%22:113,%22type%22:0%7D,%7B%22x%22:1830,%22y%22:-318,%22z%22:178,%22timestamp%22:231,%22k%22:109,%22type%22:0%7D,%7B%22x%22:1591,%22y%22:571,%22z%22:5,%22timestamp%22:331,%22k%22:112,%22type%22:0%7D,%7B%22x%22:1632,%22y%22:610,%22z%22:52,%22timestamp%22:1845,%22k%22:87,%22type%22:0%7D,%7B%22x%22:1925,%22y%22:866,%22z%22:456,%22timestamp%22:1947,%22k%22:70,%22type%22:0%7D,%7B%22x%22:1941,%22y%22:745,%22z%22:676,%22timestamp%22:2053,%22k%22:108,%22type%22:0%7D,%7B%22x%22:1354,%22y%22:134,%22z%22:92,%22timestamp%22:2153,%22k%22:75,%22type%22:0%7D,%7B%22x%22:1462,%22y%22:112,%22z%22:130,%22timestamp%22:2281,%22k%22:82,%22type%22:0%7D,%7B%22x%22:2043,%22y%22:412,%22z%22:657,%22timestamp%22:2382,%22k%22:95,%22type%22:0%7D,%7B%22x%22:1763,%22y%22:65,%22z%22:348,%22timestamp%22:2482,%22k%22:68,%22type%22:0%7D,%7B%22x%22:2169,%22y%22:445,%22z%22:740,%22timestamp%22:2584,%22k%22:113,%22type%22:0%7D,%7B%22x%22:1881,%22y%22:156,%22z%22:455,%22timestamp%22:5753,%22k%22:72,%22type%22:0%7D,%7B%22x%22:2234,%22y%22:233,%22z%22:808,%22timestamp%22:5853,%22k%22:96,%22type%22:0%7D,%7B%22x%22:2002,%22y%22:-257,%22z%22:545,%22timestamp%22:5953,%22k%22:85,%22type%22:0%7D,%7B%22x%22:3115,%22y%22:782,%22z%22:1604,%22timestamp%22:6054,%22k%22:87,%22type%22:0%7D,%7B%22x%22:1577,%22y%22:-772,%22z%22:45,%22timestamp%22:6154,%22k%22:111,%22type%22:0%7D,%7B%22x%22:2946,%22y%22:586,%22z%22:1378,%22timestamp%22:6254,%22k%22:110,%22type%22:0%7D,%7B%22x%22:2576,%22y%22:272,%22z%22:863,%22timestamp%22:7854,%22k%22:77,%22type%22:0%7D,%7B%22x%22:3101,%22y%22:1275,%22z%22:1518,%22timestamp%22:7955,%22k%22:90,%22type%22:0%7D,%7B%22x%22:3305,%22y%22:2119,%22z%22:1918,%22timestamp%22:8055,%22k%22:117,%22type%22:0%7D,%7B%22x%22:1727,%22y%22:851,%22z%22:491,%22timestamp%22:8156,%22k%22:82,%22type%22:0%7D,%7B%22x%22:2873,%22y%22:2139,%22z%22:1740,%22timestamp%22:8256,%22k%22:86,%22type%22:0%7D,%7B%22x%22:1896,%22y%22:1301,%22z%22:806,%22timestamp%22:8356,%22k%22:69,%22type%22:0%7D,%7B%22x%22:1268,%22y%22:711,%22z%22:179,%22timestamp%22:8456,%22k%22:123,%22type%22:0%7D,%7B%22x%22:2864,%22y%22:2307,%22z%22:1775,%22timestamp%22:8796,%22k%22:109,%22type%22:1%7D]&dm_img_str=V2ViR0wgMS4wIChPcGVuR0wgRVMgMi4wIENocm9taXVtKQ&dm_cover_img_str=QU5HTEUgKEFwcGxlLCBBTkdMRSBNZXRhbCBSZW5kZXJlcjogQXBwbGUgTTIgTWF4LCBVbnNwZWNpZmllZCBWZXJzaW9uKUdvb2dsZSBJbmMuIChBcHBsZS&dm_img_inter=%7B%22ds%22:[%7B%22t%22:1,%22c%22:%22bi10ZX%22,%22p%22:[1309,63,898],%22s%22:[148,262,328]%7D],%22wh%22:[3979,6018,11],%22of%22:[588,834,75]%7D&x-bili-device-req-json=%7B%22platform%22:%22web%22,%22device%22:%22pc%22%7D&x-bili-web-req-json=%7B%22spm_id%22:%22333.999%22%7D&w_rid=a0dce3a9bb68f3e43b09e524e034c6a6&wts=1726496750
       */
      export interface UserMblog {
        code: number
        message: string
        ttl: number
        data: {
          has_more: boolean
          items: Array<{
            basic: {
              comment_id_str: string
              comment_type: number
              jump_url?: string
              like_icon: {
                action_url: string
                end_url: string
                id: number
                start_url: string
              }
              rid_str: string
            }
            id_str: string
            modules: {
              module_author: {
                avatar: {
                  container_size: { height: number; width: number }
                  fallback_layers: {
                    is_critical_group: boolean
                    layers: Array<{
                      general_spec: {
                        pos_spec: { axis_x: number; axis_y: number; coordinate_pos: number }
                        render_spec: { opacity: number }
                        size_spec: { height: number; width: number }
                      }
                      layer_config: {
                        is_critical?: boolean
                        tags: {
                          AVATAR_LAYER?: {}
                          GENERAL_CFG?: {
                            config_type: number
                            general_config: {
                              web_css_style: {
                                'borderRadius': '50%'
                                'background-color'?: 'rgb(255,255,255)'
                                'border'?: '2px solid rgba(255,255,255,1)'
                                'boxSizing'?: 'border-box'
                              }
                            }
                          }
                          PENDENT_LAYER?: {}
                          ICON_LAYER?: {}
                        }
                      }
                      resource: {
                        res_image: {
                          image_src: {
                            placeholder?: number
                            remote?: {
                              bfs_style: 'widget-layer-avatar'
                              url: string
                            }
                            src_type: number
                            local?: number
                          }
                        }
                        res_type: number
                      }
                      visible: boolean
                    }>
                  }
                  mid: string
                }
                decorate: {
                  card_url: string
                  fan: {
                    /**
                     * "#34B982"
                     */
                    color: string
                    is_fan: boolean
                    num_str: string
                    number: number
                  }
                  id: number
                  jump_url: string
                  /**
                   * "明前奶绿爱丽丝动态卡片粉丝"
                   */
                  name: string
                  type: number
                }
                face: string
                face_nft: boolean
                following: boolean
                /**
                 * "//space.bilibili.com/2132180406/dynamic"
                 */
                jump_url: string
                label: string
                mid: number
                /**
                 * "明前奶绿"
                 */
                name: string
                official_verify: { desc: string; type: number }
                pendant: {
                  expire: number
                  image: string
                  image_enhance: string
                  image_enhance_frame: string
                  n_pid: number
                  /**
                   * "明前奶绿头像框"
                   */
                  name: string
                  pid: number
                }
                pub_action: string
                pub_location_text?: string
                pub_time: string
                pub_ts: number
                type: 'AUTHOR_TYPE_NORMAL'
                vip: {
                  avatar_subscript: number
                  avatar_subscript_url: string
                  due_date: number
                  label: {
                    /**
                     * "#FB7299"
                     */
                    bg_color: string
                    bg_style: number
                    border_color: string
                    img_label_uri_hans: string
                    img_label_uri_hans_static: string
                    img_label_uri_hant: string
                    img_label_uri_hant_static: string
                    /**
                     * "annual_vip"
                     */
                    label_theme: string
                    path: string
                    /**
                     * "年度大会员"
                     */
                    text: string
                    text_color: '#FFFFFF'
                    use_img_label: boolean
                  }
                  nickname_color: '#FB7299'
                  status: number
                  theme_type: number
                  type: number
                }
              }
              module_dynamic: {
                additional: null
                desc: {
                  rich_text_nodes: Array<{
                    orig_text: string
                    text: string
                    type: 'RICH_TEXT_NODE_TYPE_AT' | 'RICH_TEXT_NODE_TYPE_TEXT'
                    rid?: string
                  }>
                  text: string
                } | null
                major: {
                  opus: {
                    fold_action: Array<'展开' | '收起'>
                    jump_url: string
                    pics: Array<{
                      height: number
                      size: number
                      url: string
                      width: number
                    }>
                    summary: {
                      rich_text_nodes: Array<{
                        orig_text: string
                        text: string
                        type:
                          | 'RICH_TEXT_NODE_TYPE_EMOJI'
                          | 'RICH_TEXT_NODE_TYPE_TEXT'
                          | 'RICH_TEXT_NODE_TYPE_TOPIC'
                          | 'RICH_TEXT_NODE_TYPE_WEB'
                        jump_url?: string
                        style?: null
                        emoji?: {
                          icon_url: string
                          size: number
                          text: string
                          type: number
                        }
                      }>
                      text: string
                    }
                    title: null
                  }
                  type: 'MAJOR_TYPE_OPUS'
                } | null
                topic: null
              }
              module_more: {
                three_point_items: Array<{
                  /**
                   * "举报"
                   */
                  label: ''
                  type: 'THREE_POINT_REPORT'
                }>
              }
              module_stat: {
                comment: { count: number; forbidden: boolean }
                forward: { count: number; forbidden: boolean }
                like: { count: number; forbidden: boolean; status: boolean }
              }
              module_tag?: { text: string }
              module_interaction?: {
                items: Array<{
                  desc: {
                    rich_text_nodes: Array<{
                      orig_text: string
                      text: string
                      type: 'RICH_TEXT_NODE_TYPE_AT' | 'RICH_TEXT_NODE_TYPE_TEXT'
                      rid?: string
                    }>
                    text: string
                  }
                  type: number
                }>
              }
            }
            type: 'DYNAMIC_TYPE_DRAW' | 'DYNAMIC_TYPE_FORWARD'
            visible: boolean
            orig?: {
              basic: {
                comment_id_str: string
                comment_type: number
                jump_url?: string
                like_icon: {
                  action_url: string
                  end_url: string
                  id: number
                  start_url: string
                }
                rid_str: string
              }
              id_str: string
              modules: {
                module_author: {
                  avatar: {
                    container_size: { height: number; width: number }
                    fallback_layers: {
                      is_critical_group: boolean
                      layers: Array<{
                        general_spec: {
                          pos_spec: { axis_x: number; axis_y: number; coordinate_pos: number }
                          render_spec: { opacity: number }
                          size_spec: { height: number; width: number }
                        }
                        layer_config: {
                          is_critical?: boolean
                          tags: {
                            AVATAR_LAYER?: {}
                            GENERAL_CFG?: {
                              config_type: number
                              general_config: {
                                web_css_style: {
                                  'borderRadius': '50%'
                                  'background-color'?: 'rgb(255,255,255)'
                                  'border'?: '2px solid rgba(255,255,255,1)'
                                  'boxSizing'?: 'border-box'
                                }
                              }
                            }
                            PENDENT_LAYER?: {}
                            ICON_LAYER?: {}
                          }
                        }
                        resource: {
                          res_image: {
                            image_src: {
                              placeholder?: number
                              remote?: {
                                bfs_style: 'widget-layer-avatar'
                                url: string
                              }
                              src_type: number
                              local?: number
                            }
                          }
                          res_type: number
                        }
                        visible: boolean
                      }>
                    }
                    mid: string
                  }
                  decorate: {
                    card_url: string
                    fan: {
                      /**
                       * "#34B982"
                       */
                      color: string
                      is_fan: boolean
                      num_str: string
                      number: number
                    }
                    id: number
                    jump_url: string
                    /**
                     * "明前奶绿爱丽丝动态卡片粉丝"
                     */
                    name: string
                    type: number
                  }
                  face: string
                  face_nft: boolean
                  following: boolean
                  /**
                   * "//space.bilibili.com/2132180406/dynamic"
                   */
                  jump_url: string
                  label: string
                  mid: number
                  /**
                   * 明前奶绿
                   */
                  name: string
                  official_verify: { desc: string; type: number }
                  pendant: {
                    expire: number
                    image: string
                    image_enhance: string
                    image_enhance_frame: string
                    n_pid: number
                    /**
                     * 明前奶绿头像框
                     */
                    name: string
                    pid: number
                  }
                  pub_action: string
                  pub_location_text?: string
                  pub_time: string
                  pub_ts: number
                  type: 'AUTHOR_TYPE_NORMAL'
                  vip: {
                    avatar_subscript: number
                    avatar_subscript_url: string
                    due_date: number
                    label: {
                      bg_color: '#FB7299'
                      bg_style: number
                      border_color: string
                      img_label_uri_hans: string
                      img_label_uri_hans_static: string
                      img_label_uri_hant: string
                      img_label_uri_hant_static: string
                      label_theme: 'annual_vip'
                      path: string
                      /**
                       * 年度大会员
                       */
                      text: string
                      text_color: '#FFFFFF'
                      use_img_label: boolean
                    }
                    nickname_color: '#FB7299'
                    status: number
                    theme_type: number
                    type: number
                  }
                }
                module_dynamic: {
                  additional: null
                  desc: null
                  major: {
                    opus: {
                      fold_action: Array<'展开' | '收起'>
                      jump_url: string
                      pics: Array<{
                        height: number
                        size: number
                        url: string
                        width: number
                      }>
                      summary: {
                        rich_text_nodes: Array<{
                          orig_text: string
                          text: string
                          type:
                            | 'RICH_TEXT_NODE_TYPE_EMOJI'
                            | 'RICH_TEXT_NODE_TYPE_TEXT'
                            | 'RICH_TEXT_NODE_TYPE_TOPIC'
                            | 'RICH_TEXT_NODE_TYPE_WEB'
                          jump_url?: string
                          style?: null
                          emoji?: {
                            icon_url: string
                            size: number
                            text: string
                            type: number
                          }
                        }>
                        text: string
                      }
                      title: null
                    }
                    type: 'MAJOR_TYPE_OPUS'
                  }
                  topic: null
                }
              }
              type: 'DYNAMIC_TYPE_DRAW' | 'DYNAMIC_TYPE_FORWARD'
              visible: boolean
            }
          }>
          offset: string
          update_baseline: string
          update_num: number
        }
      }

      /**
       * 装扮列表
       * 无需登录
       * https://api.bilibili.com/x/garb/v2/mall/partition/item/list?group_id=0&location=mall_index_default_feed&part_id=6&pn=${page}&ps=20&sort_type=2
       */
      export interface Suits {
        code: number
        message: string
        ttl: number
        data: {
          page: {
            /** 装扮总数 */
            total: number
            /** 当前页面 */
            pn: number
            /** 每页数量，最多20 */
            ps: number
          }
          /** 并非完整的 suit data，但是懒得单独搞了，也用不到 */
          list: SuitData[]
          /** `"pool::0"` */
          offset_info: string
          group: string
        }
      }

      /**
       * 装扮详情 API
       *
       * 下面的 API 来自商城首页
       * 默认要传 buvid 和 csrf，未登录的也可以，但也可以不传，目前低风控
       * from_id 是当前装扮作者，可不传
       * @link https://api.bilibili.com/x/garb/v2/mall/suit/detail?buvid=<string>&csrf=<string>&from=goods_post&from_id=3493134470220310&item_id=212054401&part=suit
       *
       * 下面的 API 来自个人空间首页，与商城略有区别，大体上是已知的，但是获取不到剩余库存
       * @link https://api.bilibili.com/x/garb/v2/user/suit/benefit?item_id=2452&part=skin
       */
      export interface Suit {
        code: number
        message: string
        ttl: number
        // 个人空间 api 的话，如果传入 id 不存在会直接变成 data，但是商城不会😅
        data?: SuitData
      }

      /** 装扮销售类型 */
      export type SuitSaleType = 'pay'

      /** 装扮状态 */
      export type SuitState = 'active' | string

      /** Data of `Suit` */
      export interface SuitData {
        /** 唯一 ID，当传入的装扮不存在时，此字段不存在😅 */
        item_id?: number
        /** 装扮名称，，当传入的装扮不存在时，此字段不存在😅 */
        name?: string
        group_id?: number
        group_name?: string
        part_id?: number
        /** 装扮是否上线 */
        state?: SuitState
        /** 装扮额外属性，当装扮不存在时，此属性直接不存在 */
        properties?: {
          /** 我滴个龟龟呀~2.0彩虹龟龟来啦。预留编号1 3 8 22 55 */
          desc: string
          /** 老版本装扮中此字段为用户名，新版本中变成了一串诡异的数字字符串😅，只在获取老版本下架装扮时会用到 */
          fan_id: string
          fan_item_ids: string
          /** 用户 UID */
          fan_mid: string
          /**
           * `'#7c9a3f'`
           * @deprecated no longer used
           */
          fan_no_color?: string
          /**
           * `'习惯躺在藤椅上沐浴阳光，如向阳的花束般的女孩，似乎还隐藏着别具魅力的秘密。与明前奶绿签订契约，成为「LAPLACE」花店尊贵的客人吧！\n'`
           * @deprecated no longer used
           */
          fan_recommend_desc?: string
          /**
           * `'url'`
           * @deprecated no longer used
           */
          fan_recommend_jump_type?: string
          /**
           * `'https://space.bilibili.com/2132180406?spm_id_from=333.337.0.0'`
           * @deprecated no longer used
           */
          fan_recommend_jump_value?: string
          fan_share_image: string
          gray_rule: StringBoolean
          gray_rule_type: string
          /** 老版本无 fan_user 字段的装扮会拥有此字段 */
          image?: string
          /** 老版本无 fan_user 字段的装扮会拥有此字段，该值通常为b站官方的一个傻笑头像图，不要使用 */
          image_avatar?: string
          /** 老版本无 fan_user 字段的装扮会拥有此字段，SVGA 特有 */
          image_ani?: string
          /** 老版本无 fan_user 字段的装扮会拥有此字段，SVGA 特有 */
          image_ani_cut?: string
          /** 老版本无 fan_user 字段的装扮会拥有此字段，当拥有 `image_ani` 和 `image_ani_cut` 这两个 SVGA 字段时，会拥有此字段 */
          image_preview?: string
          /** 老版本无 fan_user 字段的装扮会拥有此字段，个别表情包又 gif 字段😅 */
          image_gif?: string
          /** 新版本拥有 fan_user 的字段会拥有此字段 */
          image_cover?: string
          image_cover_color?: string
          image_cover_long?: string
          /** 装扮宣传文案（图片格式） */
          image_desc?: string

          // https://laplace.live/suits/1016
          /** 老版本不知道是什么鬼玩意的空间装扮字串😅 */
          image1_landscape?: string
          image2_landscape?: string
          image3_landscape?: string

          /** 老版本不知道是什么鬼玩意的空间装扮字串😅 */
          image1_portrait?: string
          image2_portrait?: string
          image3_portrait?: string

          /** 老版本不知道是什么鬼玩意的粉丝编号字串😅 */
          fan_no_image?: string

          /** 老版本不知道是什么鬼玩意的空间首页视频字串😅 */
          space_1_mp4_horizontal?: string
          space_1_mp4_play_mode?: string
          space_1_mp4_vertical?: string
          space_2_mp4_horizontal?: string
          space_2_mp4_play_mode?: string
          space_2_mp4_vertical?: string
          space_3_mp4_horizontal?: string
          space_3_mp4_play_mode?: string
          space_3_mp4_vertical?: string

          /** 老版本进度条素材 */
          image_preview_small?: string
          loading_frame_url?: string
          loading_url?: string

          /** 老版本播放按钮素材 */
          static_icon_image?: string

          /**
           * 测试用😅
           * https://laplace.live/suits/33529
           */
          img_url_full?: string
          img_url_normal?: string
          img_url_pad?: string

          item_id_card: string
          item_id_emoji_package: string
          item_stock_surplus: string
          /**
           * 发行商 UID，老装扮也可能不存在此字段😅
           * ie. https://laplace.live/suit/36039
           * ie. https://laplace.live/suit/1222
           */
          owner_uid?: string
          rank_investor_show: StringBoolean
          /**
           * @deprecated 新装扮不再使用
           */
          realname_auth?: StringBoolean
          /** 永久装扮价格，字符串，例如 6900，售价则为 6900 / 100 = 69 CNY */
          sale_bp_forever_raw: string
          /** 基础装扮，按月购买，价格为每月价格 */
          sale_bp_pm_raw: string
          /** 每次购买上线 */
          sale_buy_num_limit: string
          /** 总数量，返回字串数字，个别绝版和下架装扮不能存在此字段 😅 */
          sale_quantity?: string
          /** 是否限购，返回 `"true"` or `"false"` 😅 */
          sale_quantity_limit: StringBoolean
          /** 销售区域限制，`全球`，老装扮可能不存在此字段 */
          sale_region_ip_limit?: string
          sale_reserve_switch: StringBoolean
          sale_sku_id_1: string
          sale_sku_id_2: string
          /** 销售开始时间，个别装扮返回的值是 `"-1"`，需要特殊解析，个别绝版和下架装扮甚至不存在此字段😅 */
          sale_time_begin?: string
          /** `pay` */
          sale_type: string
          suit_card_type: string
          type: string
        }
        /** 当前装扮是否属于某个特殊折扣活动 */
        current_activity?: {
          /** `"open_platform_vip_discount"` */
          type: string
          time_limit: boolean
          /** `428738575` */
          time_left: number
          /** `"大会员平台折扣"` */
          tag: string
          /** `480` */
          price_bp_month: number
          /** `2000` */
          price_bp_forever: number
          /** `"大会员平台折扣"` */
          type_month: string
          tag_month: string
          time_limit_month: boolean
          time_left_month: number
        }
        next_activity?: null
        current_sources?: null
        finish_sources?: null
        sale_left_time?: number
        sale_time_end?: number
        /** 个人空间的api不存在此字段😅 */
        sale_surplus?: number
        sale_count_desc?: string
        total_count_desc?: string
        tag?: string
        jump_link?: string
        sales_mode?: number
        /** 当装扮不存在时，此属性直接返回 null */
        suit_items: SuitItems | null
        /** 装扮商城中此字段必存在；个人空间中此字段为空 */
        fan_user?: {
          mid: number
          nickname: string
          avatar: string
        }
        unlock_items: null
        /** 当装扮不存在时，此属性直接返回 null */
        activity_entrance: {
          id: number
          item_id: number
          title: string
          image_cover: string
          jump_link: string
        } | null
      }

      /** 装扮素材对象 */
      export interface SuitItems {
        card: Card[]
        card_bg: CardBg[]
        loading?: Loading[]
        play_icon?: PlayIcon[]
        emoji_package: EmojiPackage[]
        emoji?: EmojiPackageItem[]
        skin: Skin[]
        space_bg: SpaceBg[]
        thumbup: Thumbup[]
      }

      /** 装扮编号卡片 */
      export interface Card {
        item_id: number
        name: string
        state: SuitState
        tab_id: number
        suit_item_id: number
        properties: {
          /** `"#89A848"` */
          fan_no_color?: string
          fans_image?: string
          fans_material_id: string
          goods_type: string
          hot: string
          image: string
          sale_type: string
          image_preview_small?: string
        }
        current_activity: null
        next_activity: null
        current_sources: null
        finish_sources: null
        sale_left_time: number
        sale_time_end: number
        sale_surplus: number
        items: null
      }

      /** 卡片背景 */
      export interface CardBg {
        item_id: number
        name: string
        state: SuitState
        tab_id: number
        suit_item_id: number
        properties: {
          fan_no_color: string
          goods_type: string
          image: string
          image_preview_small: string
          sale_type: string
        }
        current_activity: null
        next_activity: null
        current_sources: null
        finish_sources: null
        sale_left_time: number
        sale_time_end: number
        sale_surplus: number
        items: null
      }

      /** 表情包 */
      export interface EmojiPackage {
        item_id: number
        name: string
        state: SuitState
        tab_id: number
        suit_item_id: number
        properties: {
          addable: string
          biz: string
          goods_type: string
          image: string
          is_symbol: string
          item_emoji_list: string
          permanent: string
          preview: string
          recently_used: string
          recommend: string
          removable: string
          sale_type: SuitSaleType
          setting_pannel_not_show: string
          size: string
          sortable: string
        }
        current_activity: null
        next_activity: null
        current_sources: null
        finish_sources: null
        sale_left_time: number
        sale_time_end: number
        sale_surplus: number
        items: EmojiPackageItem[]
      }

      /** 表情包单个项目，给单独提取出来主要是为了给收藏集里的兑换表情包用 */
      export interface EmojiPackageItem {
        item_id: number
        name: string
        state: SuitState
        tab_id: number
        suit_item_id: number
        properties: {
          image: string
          sale_type: SuitSaleType
        }
        current_activity: null
        next_activity: null
        current_sources: null
        finish_sources: null
        sale_left_time: number
        sale_time_end: number
        sale_surplus: number
      }

      /** 进度条 */
      export interface Loading {
        item_id: number
        name: string
        state: string
        tab_id: number
        suit_item_id: number
        properties: {
          gray_rule: string
          gray_rule_type: string
          image_preview_small: string
          loading_frame_url: string
          loading_url: string
          realname_auth: string
          ver: string
        }
        current_activity: null
        next_activity: null
        current_sources: null
        finish_sources: null
        sale_left_time: number
        sale_time_end: number
        sale_surplus: number
        items: null
      }

      /** 播放按钮 */
      export interface PlayIcon {
        item_id: number
        name: string
        state: string
        tab_id: number
        suit_item_id: number
        properties: {
          drag_left_png: string
          drag_right_png: string
          gray_rule: string
          gray_rule_type: string
          middle_png: string
          realname_auth: string
          squared_image: string
          static_icon_image: string
          ver: string
        }
        current_activity: null
        next_activity: null
        current_sources: null
        finish_sources: null
        sale_left_time: number
        sale_time_end: number
        sale_surplus: number
        items: null
      }

      /** app 皮肤 */
      export interface Skin {
        item_id: number
        name: string
        state: SuitState
        tab_id: number
        suit_item_id: number
        properties: {
          color: string
          color_mode: string
          color_second_page: string
          goods_type: string
          head_bg: string
          head_myself_mp4_bg: string
          head_myself_mp4_play: string
          head_myself_squared_bg: string
          head_tab_bg: string
          image_cover: string
          image_preview: string
          package_md5: string
          package_url: string
          tail_bg: string
          tail_color: string
          tail_color_selected: string
          tail_icon_channel: string
          tail_icon_dynamic: string
          tail_icon_main: string
          tail_icon_mode: string
          tail_icon_myself: string
          tail_icon_pub_btn_bg: string
          tail_icon_selected_channel: string
          tail_icon_selected_dynamic: string
          tail_icon_selected_main: string
          tail_icon_selected_myself: string
          tail_icon_selected_pub_btn_bg: string
          tail_icon_selected_shop: string
          tail_icon_shop: string
          ver: string
        }
        current_activity: null
        next_activity: null
        current_sources: null
        finish_sources: null
        sale_left_time: number
        sale_time_end: number
        sale_surplus: number
        items: null
      }

      /** 空间背景 */
      export interface SpaceBg {
        item_id: number
        name: string
        state: SuitState
        tab_id: number
        suit_item_id: number
        properties: {
          fan_no_color: string
          goods_type: string
          image1_landscape: string
          image1_portrait: string
        }
        current_activity: null
        next_activity: null
        current_sources: null
        finish_sources: null
        sale_left_time: number
        sale_time_end: number
        sale_surplus: number
        items: null
      }

      /** 点赞效果 */
      export interface Thumbup {
        item_id: number
        name: string
        state: string
        tab_id: number
        suit_item_id: number
        properties: {
          gray_rule: string
          gray_rule_type: string
          image_ani: string
          image_ani_cut: string
          image_preview: string
          realname_auth: string
        }
        current_activity: null
        next_activity: null
        current_sources: null
        finish_sources: null
        sale_left_time: number
        sale_time_end: number
        sale_surplus: number
        items: null
      }

      /**
       * 收藏集列表，从 iOS app 通过 Surge 抓取而来
       * 无需登录
       * https://api.bilibili.com/x/vas/dlc_act/act/list?scene=1&site=0
       */
      export interface Collections {
        code: number
        message: string
        ttl: number
        data: {
          list: {
            act_id: number
            act_name: string
            act_pic: string
            /** `"9900"` */
            sale_price: number
            act_desc: string
            /** `'预约中'` */
            tag: string
            lottery_id: number
            lottery_type: number
            act_link: string
          }[]
          /** 是否还有下一页 */
          is_more: boolean
          /** 下一页所对应的 site 参数 */
          site: number
        }
      }

      /**
       * 收藏集基础 API
       * - csrf 可不传
       * @link https://api.bilibili.com/x/vas/dlc_act/act/basic?act_id=102108&csrf=<string>
       */
      export interface Collection {
        code: number
        message: string
        ttl: number
        /** 当输入无效 id 时，data 会直接返回 null */
        data: CollectionData | null
      }

      /** Data of `Collection` */
      export interface CollectionData {
        /** `"神妖惑众·梨安收藏集"` */
        act_title: string
        cur_time: number
        start_time: number
        end_time: number
        pre_start_time: number
        pre_end_time: number
        is_booked: number
        act_desc: string
        /** 抽奖列表，通常只有一个，打包的会有多个，例如 VR */
        lottery_list: CollectionLotteryList[]
        share_info: {
          main_title: string
          sub_title: string
          share_content: string
        }
        /** 宣传横幅 */
        act_y_img: string
        animation_info: {
          animation_draw_url: { [key: string]: string }
          animation_entry_url: { [key: string]: string }
        }
        total_book_cnt: number
        total_buy_cnt: number
        horizontal_card_light_url: string
        horizontal_card_shadow_url: string
        vertical_card_light_url: string
        vertical_card_shadow_url: string
        is_pre: number
        is_pre_risk: number
        is_collector_rank: number
        collect_list: null
        display_title: string
        effective_forever: number
        guide_info: {
          title: string
          guide_content: string
          jump_url: string
        }
        is_can_donate: number
        is_up_chain: number
        related_mids: string[] | null
        is_vip: boolean
        task_list: null
        popup_info: null
        act_desc_light: string
        not_need_realname: number
        product_introduce: string
        app_head_show: string
        app_space_show: string
        /** stringified json */
        collector_medal_info: string
        topic: null
        physical_exchange: boolean
        act_square_img: string
        tab_lottery_id: number
      }

      /** 收藏集 pack */
      export interface CollectionLotteryList {
        /** 当前 pack 的唯一 ID，获取详情 API 时会用到 */
        lottery_id: number
        lottery_name: string
        display_time: number
        start_time: number
        end_time: number
        lottery_image: string
        draw_cnt: number
        goods_id: number
        price: number
        goods_name: string
        lottery_desc: string
        item_total_cnt: number
        item_owned_cnt: number
        total_sale_amount: number
        lottery_type: number
        effective_forever: number
        discount: {
          /** `"vip_first_buy"` */
          discount_type: string
          /** `"大会员首抽"` */
          discount_tag: string
          /** 9900 */
          origin_price: number
          /** 4900 */
          price: number
        }
        free: number
        sale_mode: number
      }

      /**
       * 收藏集详情
       * - csrf 可不传
       * - lottery_id 需要从基础信息获取
       * @link https://api.bilibili.com/x/vas/dlc_act/lottery/detail?act_id=100600&csrf=<string>&lottery_id=100616
       */
      export interface CollectionDetails {
        code: number
        message: string
        ttl: number
        data: CollectionDetailsData
      }

      /** Data of `CollectionDetails` */
      export interface CollectionDetailsData {
        lottery_id: number
        name: string
        display_time: number
        start_time: number
        end_time: number
        draw_left_cnt: number
        goods_id: number
        price: number
        goods_name: string
        lottery_desc: string
        item_total_cnt: number
        item_owned_cnt: number
        total_sale_amount: number
        item_list: CollectionDetailsListItem[]
        effective_forever: number
        lottery_bottom: {
          draw_cnt: number
          bottom_cnt: number
        }
      }

      /** Item of `CollectionDetailsData` */
      export interface CollectionDetailsListItem {
        item_type: number
        scarcity: number
        card_info: {
          card_type_info: {
            id: number
            name: string
            overview_image: string
            content: CollectionDetailsListItemContent
            material_type: number
            theme: string
            scarcity: number
            layout: number
            width: number
            height: number
            face_overflow: null
            material_sub_type: number
            overview_image_watermarks: {
              scene: number
              watermark_image: string
            }[]
            watermark_animations:
              | {
                  scene: number
                  watermark_animation: string
                }[]
              | null
            space_background: CollectionDetailsListItemContent
            card_no_style: {
              color_format: {
                start_point: string
                end_point: string
                colors: string[]
                gradients: number[]
              }
            }
          }
          is_new_tag: number
          is_up_tag: number
          tag: {
            image_url: string
            content: string
          } | null
          is_limited_card: number
          stock_info: null
          card_scarcity: number
        }
      }

      /** Props of `CollectionDetailsListItem` */
      export interface CollectionDetailsListItemContent {
        image: {
          default_image: string
          gyroscope: null
          location: string
        } | null
        animation: {
          animation_video_urls: string[]
          animation_backup_image: string
          animation_first_frame: string
          is_mute: boolean
          animation_url: string
          subtitles_url: string
          duration: number
          width: number
          height: number
          location: string
          compressed_w_h: string
        } | null
      }

      /**
       * 收藏集首页详情
       * - csrf 可不传
       * - lottery_id 需要从基础信息获取
       * @link https://api.bilibili.com/x/vas/dlc_act/lottery_home_detail?act_id=161&csrf=<string>&lottery_id=75
       */
      export interface CollectionInfo {
        code: number
        message: string
        ttl: number
        data: CollectionInfoData
      }

      /** Data of `CollectionInfo` */
      export interface CollectionInfoData {
        lottery_id: number
        name: string
        item_list: {
          item_type: number
          card_info: {
            card_type_id: number
            card_name: string
            card_img: string
            card_type: number
            video_list: string[] | null
            is_physical_orientation: number
            card_scarcity: number
            is_mute: number
            width: number
            height: number
            card_ext_text: string
            card_img_download: string
            video_list_download: string[] | null
            subtitles_url: string
            play: null
            tag: null
            card_sub_type: number
            is_new_tag: number
            is_up_tag: number
          }
        }[]
        /**
         * 某些特殊 lottery 该值仍然可能为null 😅
         * 例如 https://workers.vrp.moe/bilibili/collection-info/279/257
         */
        collect_list: {
          /** 兑换项目 */
          collect_infos: CollectionInfoDataItem[]
          /** 进度解锁兑换项目 */
          collect_chain: CollectionInfoDataItem[] | null
        } | null
        button_bubble: null
        guide_info: null
      }

      /** Item of CollectionInfoData */
      export interface CollectionInfoDataItem {
        collect_id: number
        start_time: number
        end_time: number
        redeem_text: string
        redeem_item_type: number
        /** 解锁的表情包之类的额外内容可通过此 ID 获取，该 ID 可以拿到 /bilibili/suits/ 下请求到具体数据 */
        redeem_item_id: string
        redeem_item_name: string
        redeem_item_image: string
        owned_item_amount: number
        require_item_amount: number
        has_redeemed_cnt: number
        effective_forever: number
        redeem_item_image_download: string
        card_item: {
          card_type_info: null
          play: null
          tag: null
          card_asset_info: null
        } | null
        jump_url: string
        /** 兑换类型，目前已知 `scarcity` 可通过 /bilibili/suits 兑换，`lottery_num` 未知 */
        redeem_cond_type: string
        remain_stock: number
        total_stock: number
        lottery_id: number
        /** `"任务奖励限定"` */
        reward_tag: string
        redeem_detail_image: string
        redeem_detail_videos: null
        sort: number
        redeem_items_optional: null
      }

      /**
       * 直播分区
       *
       * 访问 https://live.bilibili.com/ 可触发
       * @link https://api.live.bilibili.com/xlive/web-interface/v1/index/getWebAreaList?source_id=2
       */
      export interface LiveGetWebAreaList {
        code: number
        message: string
        ttl: number
        data: {
          data: {
            id: number
            /**
             * （父级）分区标题
             *
             * - 互动玩法
             * - 单机游戏
             * - 娱乐
             * - 帮我玩
             * - 手游
             * - 生活
             * - 电台
             * - 知识
             * - 网游
             * - 聊天室
             * - 虚拟主播
             * - 购物
             * - 赛事
             */
            name: string
            list: {
              /** 分区 id */
              id: string
              /** 父分区 ID */
              parent_id: string
              /** （父级）分区标题 */
              parent_name: string
              old_area_id: string
              name: string
              pinyin: string
              act_id: string
              hot_status: number
              pk_status: string
              lock_status: string
              pic: string
              area_type: number
            }[]
          }[]
          expid: number
        }
      }

      /**
       * 直播列表
       * @link https://api.live.bilibili.com/xlive/web-interface/v1/second/getList?platform=web&parent_area_id=9&area_id=744&sort_type=online&page=2
       */
      export interface LiveGetList {
        code: number
        message: string
        ttl: number
        data: {
          banner: LiveGetListBanner[]
          new_tags: LiveGetListTag[]
          list: LiveGetListItem[]
          count: number
          /** 是否有下一页 */
          has_more: number
          vajra: null
        }
      }

      /** Banner of LiveGetList */
      export interface LiveGetListBanner {
        id: number
        title: string
        location: string
        position: number
        pic: string
        link: string
        weight: number
        room_id: number
        up_id: number
        parent_area_id: number
        area_id: number
        live_status: number
        av_id: number
        is_ad: boolean
        ad_transparent_content: null
        show_ad_icon: boolean
      }

      /** Item of LiveGetList */
      export interface LiveGetListItem {
        roomid: number
        uid: number
        title: string
        uname: string
        /** 在线人数，虚的 */
        online: number
        user_cover: string
        user_cover_flag: number
        system_cover: string
        cover: string
        show_cover: string
        link: string
        face: string
        parent_id: number
        /** 虚拟主播 */
        parent_name: string
        area_id: number
        /** 虚拟Singer */
        area_name: string
        area_v2_parent_id: number
        area_v2_parent_name: string
        area_v2_id: number
        area_v2_name: string
        session_id: string
        group_id: number
        show_callback: string
        click_callback: string
        web_pendent: string
        pk_id: number
        pendant_info: {
          [
            /** 通常为 1、2、3 */
            key: string
          ]: {
            pendent_id: number
            /** Topstar，可能为空 */
            content: string
            /** HEX color */
            color: string
            /** http://i0.hdslb.com/bfs/live/987427710521062e1776748a1e6986c61ce87043.png */
            pic: string
            position: number
            /** mobile_index_badge */
            type: string
            /** 长红计划-Topstar */
            name: string
          }
        }
        verify: {
          role: number
          /** bilibili 知名虚拟UP主、直播高能主播 */
          desc: string
          type: number
        }
        /** 角标 */
        head_box: {
          /** 大乱斗乱斗之王 */
          name: string
          /** https://i0.hdslb.com/bfs/live/fc28a2a4123154012e0ce3da1273de5f17e81b24.png */
          value: string
          /** 可能为空（`""`） */
          desc: string
        } | null
        head_box_type: number
        is_auto_play: number
        flag: number
        watched_show: {
          /** 未知，通常为 true */
          switch: boolean
          /** 6253 */
          num: number
          /** "6253" */
          text_small: string
          /** 6253人看过 */
          text_large: string
          /** https://i0.hdslb.com/bfs/live/a725a9e61242ef44d764ac911691a7ce07f36c1d.png */
          icon: string
          /** 0 */
          icon_location: number
          /** https://i0.hdslb.com/bfs/live/8d9d0f33ef8bf6f308742752d13dd0df731df19c.png */
          icon_web: string
        }
        is_nft: number
        nft_dmark: string
        play_together_goods: null
        watermark: string
      }

      /** Tag of LiveGetList */
      export interface LiveGetListTag {
        /** 981 */
        id: number
        /** TopStar */
        name: string
        /** `""` */
        icon: string
        /** `"sort_type_981"`, `"online"` */
        sort_type: string
        /** 通常为 0 */
        type: number
        /** 通常为空 */
        sub: any[]
        /** 通常为空 */
        hero_list: any[]
        /** 通常为 0 */
        sort: number
      }

      /**
       * 发送弹幕
       *
       * @link https://api.live.bilibili.com/msg/send
       */
      export interface DanmakuSend {
        code: number
        data: {
          mode_info: {
            mode: number
            show_player_type: number
            /** 需要 JSON.parse 进行解析 */
            extra: string
            user: {
              uid: number
              base: {
                name: string
                face: string
                name_color: number
                is_mystery: boolean
                risk_ctrl_info: null
                origin_info: {
                  name: string
                  face: string
                }
                official_info: {
                  role: number
                  title: string
                  desc: string
                  type: number
                }
                name_color_str: string
              }
              medal: MedalInfo
              wealth: null
              title: {
                old_title_css_id: string
                title_css_id: string
              }
              guard: null
              uhead_frame: null
              guard_leader: {
                is_guard_leader: boolean
              }
            }
          }
          /**
           * protobuf 格式
           * @deprecated 2023-10 某天之后，出于风控考虑，不再返回 `dm_v2` 数据，只返回 `null`
           * */
          dm_v2: null
        }
        /**
         * - `""` - 空消息代表发送成功😅
         * - `"你被禁言啦"`
         * - `"您发送弹幕的频率过快"`
         */
        message: string
        /** 同 `message` */
        msg: string
      }

      /**
       * 创建订单
       *
       * 目前主要用于发送醒目留言
       * @link https://api.live.bilibili.com/xlive/revenue/v1/order/createOrder
       */
      export type CreateOrder = CreateOrderSuccess | CreateOrderError

      /** Data of `CreateOrder` when success */
      export interface CreateOrderSuccess {
        code: 0
        message: string
        ttl: number
        data: {
          /** 4 代表成功，不知道为什么 */
          status: number
          /** sc 订单 id, 2411232219246412133150726 */
          order_id: string
          /** 剩余的金额，单位金币，需要除以 */
          gold: number
          /** 未知，通常为 0 */
          bp: number
          error_info: null
        }
      }

      /** Data of `CreateOrder` when error */
      export interface CreateOrderError {
        /**
         * - `1300012`: 余额不足
         * - `1300008`: 您所填写的留言未能通过审核，请重新修改~~
         * - `1300039`: 你已经被封禁
         */
        code: number
        message: string
        ttl: number
        data: null
      }
    }

    /**
     * 开放平台
     * @link https://open-live.bilibili.com/document/74eec767-e594-7ddd-6aba-257e8317c05d
     */
    namespace OpenPlatform {
      /**
       * 开放平台认证认证返回
       * @link https://live-open.biliapi.com/v2/app/start
       */
      export interface AppStart {
        code: number
        message: string
        request_id: string
        data: {
          anchor_info: {
            room_id: number
            uface: string
            uid: number
            uname: string
          }
          game_info: {
            game_id: string
          }
          websocket_info: {
            auth_body: string
            wss_link: string[]
          }
        }
        id_code: string
      }

      /**
       * 开放平台心跳返回
       * @link https://live-open.biliapi.com/v2/app/heartbeat
       */
      export interface Heartbeat {
        code: number
        /** 正常则返回 'ok' */
        message: string
        data: {}
      }

      /**
       * 开放平台批量心跳返回
       * @link https://live-open.biliapi.com/v2/app/batchHeartbeat
       */
      export interface HeartbeatBatch {
        code: number
        /** 正常则返回 'ok' */
        message: string
        data: {
          /** 心跳失败的 id */
          failed_game_ids: string[]
        }
      }

      /**
       * 开放平台关闭 game id
       * @link https://live-open.biliapi.com/v2/app/end
       */
      export interface AppEnd {
        /**
         * - 0: 正常结束
         * - 7003: 心跳过期或GameId错误
         */
        code: number
        /** 正常则返回 '0'，否则 `心跳过期或GameId错误` 之类报错 */
        message: string
        /** b站内部追踪用的，ie `'1804937190217424896'` */
        request_id: string
        data: {}
      }
    }
  }

  /**
   * WebSocket 协议的 API
   */
  namespace WebSocket {
    /**
     * 线上环境
     */
    namespace Prod {
      /**
       * 弹幕未登录打码后新增的对象，在常规 uid = 0 时此处的对象依然可以拿到 UID，可能是妥协后的产物
       */
      interface UINFO {
        /**
         * 未登录时此处为 0
         */
        uid: number
        base: {
          /**
           * 匿名时此处 **通常** 会被打码，下列事件包含 UINFO 时此处没打码，完整行为需更多调查
           *
           * - `ONLINE_RANK_V2` 没打码
           */
          name: string
          face: string
          name_color: number
          /**
           * 是否是神秘人
           */
          is_mystery: boolean
          risk_ctrl_info: null
          origin_info: {
            name: string
            face: string
          }
          official_info: {
            role: number
            title: string
            desc: string
            // -1
            type: number
          }
          name_color_str: string
        }
        /**
         * 粉丝牌对象，有时候会直接返回 null，不知道什么原因（大概风控），目前已知下列事件永远返回 null
         * - `DANMU_MSG`
         * - `LIKE_INFO_V3_CLICK`
         * - `ONLINE_RANK_V2`
         * - `POPULARITY_RED_POCKET_V2_START`
         * - `SEND_GIFT`
         */
        medal: BilibiliInternal.HTTPS.Prod.MedalInfo | null
        /**
         * 荣耀等级，已知下列事件中包含：
         * - `EFFECT_DANMAKU_MSG`
         *
         * 已知下列事件中为 null
         * - `ONLINE_RANK_V2`
         * - `SUPER_CHAT_MESSAGE`
         * - `POPULARITY_RED_POCKET_V2_START`
         * - `SEND_GIFT`
         */
        wealth: {
          /**
           * 通常为 `''`
           */
          dm_icon_key: string
          level: number
        } | null
        title: {
          /**
           * 通常为 `''`
           */
          old_title_css_id: string
          /**
           * 通常为 `''`
           */
          title_css_id: string
        } | null
        /**
         * 大航海信息，已知下列事件中包含：
         * - `ONLINE_RANK_V2`
         * - `EFFECT_DANMAKU_MSG`
         * - `SUPER_CHAT_MESSAGE`
         *
         * 已知无：
         * - `SEND_GIFT`
         */
        guard?: {
          /**
           * ie. `'2025-02-01 23:59:59'`
           */
          expired_str: string
          /**
           * 0: 白字
           * 1: 总督
           * 2: 提督
           * 3: 舰长
           */
          level: number
        }
        /**
         * 在某些特殊事件中会返回对应的头像框，目前已知返回：
         *
         * - `ONLINE_RANK_V2`
         *
         * 已知无：
         *
         * - `DANMU_MSG`
         * - `SEND_GIFT`
         * - `SUPER_CHAT_MESSAGE`
         */
        uhead_frame?: {
          /**
           * @example `https://i0.hdslb.com/bfs/live/484a499a5b0091991e32c2fd6f0968503e95ce36.png`
           */
          frame_img: string
          /**
           * @example 1750
           */
          id: number
        }
        /**
         * 舰队指挥官对象，并非所有事件都有
         *
         * @updated May 25, 2025, 10:13:44 AM PDT
         *
         * 已知有：
         * - `DANMU_MSG`
         *
         * 已知无：
         * - `SEND_GIFT`
         * - `ONLINE_RANK_V2`
         */
        guard_leader?: {
          is_guard_leader: boolean
        }
      }
      /**
       * 普通弹幕事件
       */
      interface DANMU_MSG {
        cmd: 'DANMU_MSG'
        /**
         * protobuf data
         * @deprecated 2023-10 某天之后，出于风控考虑，不再返回 `dm_v2` 数据，只返回 `''`
         */
        dm_v2: ''

        info: [
          // [0]
          [
            any, // [0][0]
            any, // [0][1]
            any, // [0][2]
            /**
             * textColor
             * [0][3]: 弹幕颜色
             */
            string,
            /**
             * timestamp
             * [0][4]: 时间戳 1692802683877
             */
            number,
            any, // [0][5]
            any, // [0][6]
            any, // [0][7]
            any, // [0][8]
            /**
             * [0][9]: sendType
             * 弹幕类型
             */
            any, // [0][9]
            /**
             * [0][10]: 未知
             * 已知: 2、5
             */
            number, // [0][10]
            string, // [0][11]: "#1453BAFF,#4C2263A2,#3353BAFF"
            number, // [0][12]: 0, danmaku type
            /**
             * [0][13]: emoteObj
             * 当弹幕不存在表情时返回为字符串形式的空对象
             * TODO: need better typing
             */
            EmoteProps | '{}',
            /**
             * 未知，通常为 `'{}'`
             */
            '{}', // [0][14]

            {
              /**
               * extra: extraJsonStr = {}
               * 返回的是 `JSON.stringify` 后的的 `ExtraMeta`
               */
              extra: string
              /**
               * 通常为 `0`
               */
              mode: number
              /**
               * 通常为 `0`
               */
              show_player_type: number
              /**
               * 用户基本信息，包含头像，在极个别情况下，此处可能会返回 null，原因未知😅
               */
              user: UINFO | null
            }, // [0][15]

            // 暂时没用到，并且大弹幕（1000 个相同弹幕后自动放大的弹幕，事件类型 `DANMU_MSG:3:7:1:1:1:1`）
            // 并不存在这个对象，会返回 `null`，因此暂时评论掉
            // {
            //   activity_identity, // ''
            //   activity_source, // 0
            //   not_show, // 0
            // } // 16
          ],

          /**
           * [1]: message
           */
          string,

          // [2]
          [
            /**
             * [2][0]: uid
             */
            number,
            /**
             * [2][1]: username
             */
            string,

            /**
             * [2][2]: userType
             * 0: normal user
             * 1: room mod
             */
            number, // 2
            /**
             * [2][3]: isVip
             */
            boolean, // 3
            /**
             * [2][4]: isSvip
             */
            boolean, // 4

            /**
             * [2][5]: isNewUser
             * < 10000: new user
             */
            boolean, // 5

            /**
             * [2][6]: phoneVerified
             * 0: not verified
             * 1: verified
             */
            number, // 6

            // username color in HEX format. normal user is empty
            /**
             * [2][7]: nameColor
             */
            string, // 7
          ],

          // [3] badge
          (
            | [
                /**
                 * [3][0]: medalLvl = 0
                 */
                number,
                /**
                 * [3][1]: medalName = ''
                 */
                string,
                /**
                 * [3][2]: medalOriginUsername
                 */
                string,
                /**
                 * [3][3]: medalRoom
                 */
                number,
                any, // [3][4]
                any, // [3][5]
                any, // [3][6]
                any, // [3][7]: medalBgStart, // 8
                any, // [3][8]: medalBorder, // 7
                any, // [3][9]: medalBgEnd, // 9
                /**
                 * [3][10]: medalGuardType
                 */
                number, // 10
                /**
                 * [3][11]: medalLightened
                 */
                number, // 11
                /**
                 * [3][12]: medalUid
                 */
                number,
              ]
            | null
          ),

          // [4]
          [
            /**
             * userLvl
             */
            number,
            any,
            /**
             * userLvlBorder
             */
            number,
            any,
            /**
             * currentRank
             * 0: 未上榜
             * 1: 榜1
             * 3: 榜2
             * 2: 榜3
             */
            number,
          ],

          // [5] 头衔，有头衔时会返回 ["title-902-1", "title-902-1"]，无头衔时会返回 ["", ""]
          [string, string], // 5

          // [6]，通常为一个数字，0，不知道是干什么的
          number, // 6

          /**
           * [7]: guardType
           * 0: 白字
           * 1: 总督
           * 2: 提督
           * 3: 舰长
           */
          number,

          // [8]，通常为 null
          any, // 8

          // [9]
          {
            // This is short and may have collision with high traffic danmakus
            // Use `timestamp` defined above
            ts: number
            // `ct` seems to be unique random string
            /**
             * 弹幕签名，配合 `extra` 字段中的 `id_str` 用于举报弹幕
             */
            ct: string
          },

          // [10]: 0
          any,

          // [11]: 0
          any,

          // [12]: null
          null,

          // [13]: null
          null,

          // [14]: 0
          number,

          // [15]: 70
          number,

          // [16]
          [
            /**
             * wealthMedalLvl = 0
             */
            number,
          ],

          /**
           * [17]
           * 当是大航海套票时，此字段存在
           */
          (
            | [
                /** 套票粉丝团 id */
                number,
                /** 套票粉丝团名称，ie. 柚饼糖 */
                string,
                /** 未知，通常为 1 */
                number,
              ]
            | null
          ),
        ]

        /**
         * 唯一 ID, ie. `"8483703561938944"`
         */
        msg_id: string

        /**
         * 未知，通常为 true
         */
        p_is_ack: boolean

        /**
         * 未知，通常为 1
         */
        p_msg_type: number
      }

      /**
       * 表情（非b豆），包括自定义表情和b站内置的长条表情
       */
      export interface EmoteProps {
        /**
         * 表情是否凸起，1 代表凸起，通常为自定义表情，0 为b站内置长条表情
         */
        bulge_display: number
        /**
         * 表情唯一标识符，例如 `"room_456117_47071"`
         */
        emoticon_unique: string
        height?: number
        in_player_area?: number
        is_dynamic?: number
        url: string
        width?: number
      }

      /**
       * 文本弹幕中的额外对象
       */
      export interface ExtraMeta {
        send_from_me: boolean
        mode: number
        color: number
        dm_type: number
        font_size: number
        player_mode: number
        show_player_type: number
        content: string
        user_hash: string
        emoticon_unique: string
        bulge_display: number
        recommend_score: number
        main_state_dm_color: string
        objective_state_dm_color: string
        direction: number
        pk_direction: number
        quartet_direction: number
        anniversary_crowd: number
        yeah_space_type: string
        yeah_space_url: string
        jump_to_url: string
        space_type: string
        space_url: string
        animation: ExtraMetaAnimationProps
        emots: { [key: string]: ExtraMetaEmoteItem }
        is_audited: boolean
        /**
         * 弹幕唯一识别 id，用于举报
         */
        id_str: string
        show_reply?: boolean
        reply_mid?: number
        reply_uname?: string
      }

      /** Props of `ExtraMeta` */
      interface ExtraMetaAnimationProps {}

      /** Item of `ExtraMeta` */
      export interface ExtraMetaEmoteItem {
        emoticon_id: number
        emoji: string
        descript: string
        url: string
        width: number
        height: number
        emoticon_unique: string
        count: number
      }

      /**
       * 弹幕撤回事件
       */
      export interface RECALL_DANMU_MSG {
        cmd: 'RECALL_DANMU_MSG'
        data: {
          /** 弹幕撤回类型，具体需更多调研
           * - 2: 似乎是ai判断有辱骂、敏感字眼，然后被撤回
           */
          recall_type: number
          /** 被撤回用户 UID */
          target_id: number
        }
      }

      /**
       * 交互事件
       */
      export interface INTERACT_WORD {
        cmd: 'INTERACT_WORD'
        data: {
          contribution: {
            grade: number
          }
          contribution_v2: {
            grade: number
            rank_type: string
            text: string
          }
          core_user_type: number
          dmscore: number
          fans_medal: FansMedalItem | null
          group_medal: null
          identities: number[]
          is_mystery: boolean
          is_spread: number
          /**
           * 交互行为
           *
           * 1: 进场
           * 2: 关注
           * 3: 分享
           * 4: 特别关注
           * 5: 互相关注
           */
          msg_type: number
          /**
           * 舰长状态：该用户在当前直播间的舰长状态
           *
           * 0: 白字
           * 1: 总督
           * 2: 提督
           * 3: 舰长
           */
          privilege_type: number
          relation_tail: {
            /** 无后缀时为空字符串，TA常看你的直播，但还没有关注 */
            tail_guide_text: string
            /**
             * icon url
             * - 1: https://i0.hdslb.com/bfs/live/b9de5d510125c6f14cd68391d5a4878fe16356b3.png
             * - 4: https://i0.hdslb.com/bfs/live/bb88734558c6383a4cfb5fa16c9749d5290d95e8.png
             */
            tail_icon: string
            /**
             * - 0: 无后缀
             * - 1: 潜在粉丝：TA常看你的直播，但还没有关注
             * - 2: 近期投喂：近期投喂过，多多与TA互动吧
             * - 3: 新粉：近期关注了你，多多与TA互动吧
             * - 4: 回归：曾经活跃过，近期与你互动较少
             */
            tail_type: number
          }
          roomid: number
          score: number
          spread_desc: string
          spread_info: string
          tail_icon: number
          tail_text: string
          timestamp: number
          /**
           * ie. 1680586909839989500
           */
          trigger_time: number
          uid: number
          uinfo: UINFO
          uname: string
          uname_color: string
        }
      }

      /**
       * 进场特效
       */
      export interface ENTRY_EFFECT {
        cmd: 'ENTRY_EFFECT'
        data: {
          id: number
          uid: number
          uinfo: UINFO
          target_id: number
          mock_effect: number
          face: string
          /**
           * 舰长状态：该用户在当前直播间的舰长状态
           *
           * 0: 白字
           * 1: 总督
           * 2: 提督
           * 3: 舰长
           */
          privilege_type: number
          /** "<%从小就很0vo%> 来了" */
          copy_writing: string
          /** "<%从小就很0vo%> 来了" */
          copy_writing_v2: string
          /** "#F7F7F7" */
          copy_color: string
          highlight_color: string
          priority: number
          basemap_url: string
          show_avatar: number
          /**
           * 持续时间，秒
           */
          effective_time: number
          web_basemap_url: string
          web_effective_time: number
          /** ms，ie. 900 */
          web_effect_close: number
          web_close_time: number
          wealth_style_info: {
            url: string
          }
          wealthy_info: {
            /** 通常为 0 😅 */
            uid: number
            level: number
            /** 通常为 0 😅 */
            level_total_score: number
            /** 通常为 0 😅 */
            cur_score: number
            /** 通常为 0 😅 */
            upgrade_need_score: number
            /** 通常为 0 */
            status: number
            /** 通常为 "" */
            dm_icon_key: string
          }
          business: number
          icon_list: string[]
          max_delay_time: number
          /** ie. 1680586909839989500 */
          trigger_time: number
          identities: number
          effect_silent_time: number
          effective_time_new: number
          /**
           * 动态背景，如果不存在则为空字符串 `""`
           */
          web_dynamic_url_webp: string
          web_dynamic_url_apng: string
          mobile_dynamic_url_webp: string
        }
      }

      /**
       * 大航海事件
       */
      export interface USER_TOAST_MSG {
        cmd: 'USER_TOAST_MSG'
        data: {
          anchor_show: boolean
          color: string
          dmscore: number
          effect_id: number
          end_time: number
          face_effect_id: number
          gift_id: number
          /**
           * 0: 白字
           * 1: 总督
           * 2: 提督
           * 3: 舰长
           */
          guard_level: number
          is_show: number
          /**
           * 大航海数量
           */
          num: number
          op_type: number
          payflow_id: string
          /**
           * 总价格，非单价，如果大航海 3 个月那此处返回 158 * 12 = 1896
           */
          price: number
          /**
           * 大航海名称，舰长、提督、总督
           */
          role_name: string
          room_effect_id: number
          start_time: number
          svga_block: number
          /**
           * 当前主播大航海总数，用于判断是否是千舰、万舰，而从使用特殊主题
           */
          target_guard_count: number
          /**
           * <%アイノラパス%> 续费了舰长，今天是TA陪伴主播的第322天
           */
          toast_msg: string
          uid: number
          /**
           * 单位，月、年
           */
          unit: string
          user_show: boolean
          username: string
        }
      }

      /**
       * 大航海事件 v2
       * 大致上线日期 Dec 12, 2024
       */
      export interface USER_TOAST_MSG_V2 {
        cmd: 'USER_TOAST_MSG_V2'
        data: {
          /** 发送方 */
          sender_uinfo: {
            uid: number
            base: {
              name: string
              /** 通常为 `''` 😅 */
              face: string
            }
          }
          /** 接收方 */
          receiver_uinfo: {
            uid: number
            base: {
              name: string
              face: string
            }
          }
          guard_info: {
            /**
             * 0: 白字
             * 1: 总督
             * 2: 提督
             * 3: 舰长
             */
            guard_level: number
            /**
             * 大航海名称，舰长、提督、总督
             */
            role_name: string
            /** 当前主播大航海总数，用于判断是否是千舰、万舰，而从使用特殊主题 */
            room_guard_count: number
            /** 未知，舰长为 2 */
            op_type: number
            /** 开通时间，1734271525 */
            start_time: number
            /** 结束时间？通常和 start_time 一样 */
            end_time: number
          }
          /** 团体打包上舰字段，未知 */
          group_guard_info: null
          pay_info: {
            /** 流水号，`'2412152205251792198695335'` */
            payflow_id: string
            /** 价格，金瓜子数量，真实货币需要 / 1000 */
            price: number
            /** 大航海数量 */
            num: number
            /**
             * 单位，月、年
             */
            unit: string
          }
          gift_info: {
            gift_id: number
          }
          /**
           * 礼物特效，对应 LiveGiftEffectItem，这个特效是只有上舰的人看到的
           */
          effect_info: {
            /**
             * 特效 ID，这个特效是只有上舰的人看到的
             *
             * @example 397
             *
             * - 397: 舰长
             * - 398: 提督
             * - 399: 总督
             */
            effect_id: number
            /**
             * 直播间特效 ID，这个特效是给直播间其他人看的
             *
             * @example 590
             *
             * - 590: 舰长
             * - 591: 提督
             * - 592: 总督
             *
             * @link https://i0.hdslb.com/bfs/live/b795b2270087db24ecf41f9a7bdf283b12717e6c.mp4
             * @link https://i0.hdslb.com/bfs/live/919538c1e28d81270301794b5a8e382d52dafaf5.mp4
             * @link https://i0.hdslb.com/bfs/live/9d43ca0163de5ce6317a9f475e4217c628d9d081.mp4
             */
            room_effect_id: number
            /**
             * 未知，舰长为 44
             */
            face_effect_id: number
            /**
             * 未知，舰长为 0
             */
            room_gift_effect_id: number
            /**
             * 直播间合体上舰特效 ID，这个特效是给其他人看的，目前似乎只有舰长
             *
             * @example 1337
             *
             * @link https://i0.hdslb.com/bfs/live/5b8b28f622a1ad22104cf6da2e2499f58cc18058.mp4
             */
            room_group_effect_id: number
          }
          /** <%アイノラパス%> 在主播明前奶绿的直播间开通了舰长，今天是TA陪伴主播的第121天 */
          toast_msg: string
          option: {
            anchor_show: boolean
            /** 是否显示？通常为 true */
            user_show: boolean
            /** 是否是团体打包上舰，通常为 0 */
            is_group: number
            /** 未知，通常为 0 */
            is_show: number
            /** 未知，通常为 0 */
            source: number
            /** 未知，通常为 0 */
            svga_block: number
            /** 颜色，`#00D1F1` */
            color: string
          }
        }
      }

      /** 盲盒礼物 of `SEND_GIFT` */
      interface SEND_GIFT_BLIND_GIFT {
        blind_gift_config_id: number
        /** 通常为 `0` */
        from: number
        /** `"爆出"` */
        gift_action: string
        /** 爆奖的虚拟价格，单位金瓜子。此处为单价，即如果爆出了两个相同的礼物，此处也会显示一个的价格 */
        gift_tip_price: number
        original_gift_id: number
        /** 盲盒名称 */
        original_gift_name: string
        /** 盲盒本身价格，单价。单位金瓜子 */
        original_gift_price: number
      }

      /**
       * 礼物事件
       */
      export interface SEND_GIFT {
        cmd: 'SEND_GIFT'
        /**
         * 发现于 Mar 26, 2025，功能未知
         */
        danmu: {
          area: 1
        }
        /**
         * 发现于 Mar 26, 2025，哎，我的 LAPLACE Event ID 怎么被你b偷了😅
         * 只有登录用户会有😅
         * @example '28554943374441238:1:1000'
         */
        msg_id?: string
        /** 功能未知，只有登录用户会有 */
        p_is_ack?: boolean
        /** 功能未知，只有登录用户会有 */
        p_msg_type?: number
        /**
         * 发送时间，只有登录用户会有😅
         * @example 1743018111820
         */
        send_time?: number
        data: {
          /**
           * 礼物动作名称，投喂，etc.
           */
          action: string
          /**
           * `<sender uid>:<receiver uid>:<gift id>:<timestamp>`
           * 此处的 timestamp 并非礼物实际发送的 timestamp，当礼物连击时，此处的 timestamp 会固定为第一个连击礼物的 timestamp，因此不要通过此处的时间戳来当作唯一 id
           *
           * batch:gift:combo_id:3546575200585982:2763:31036:1711818341.0055
           */
          batch_combo_id: string
          batch_combo_send: {
            /** `"投喂"` */
            action: string
            batch_combo_id: string
            batch_combo_num: number
            /**
             * 盲盒礼物，非盲盒礼物时返回 `null`
             */
            blind_gift: SEND_GIFT_BLIND_GIFT | null
            gift_id: number
            gift_name: string
            gift_num: number
            send_master: null
            uid: number
            uname: string
          }
          beatId: string
          /**
           * 当前礼物所处的事务源
           * - 'Live' - 普通直播
           * - 'voice_chat_room' - 语音连麦，多人连麦时会影响 `receiver`，即 `receive_user_info`
           */
          biz_source: string
          /**
           * 盲盒礼物，非盲盒礼物时返回 `null`
           */
          blind_gift: SEND_GIFT_BLIND_GIFT | null
          broadcast_id: number
          /**
           * 'gold': 金瓜子，现金购买
           * 'silver': 银瓜子，兑换的礼物
           */
          coin_type: string
          combo_resources_id: number
          combo_send: {
            /** `"投喂"` */
            action: string
            combo_id: string
            combo_num: number
            gift_id: number
            gift_name: string
            gift_num: number
            send_master: null
            uid: number
            uname: string
          }
          combo_stay_time: number
          /**
           * 当礼物连击时：
           * 第一个礼物，会携带 `batch_combo_send` 和 `combo_send`，但后续的连击会让上述 property
           * 返回 null，因此只能通过单独的 `combo_total_coin` 去判断
           *
           * 例如用户赠送 价值 5 元（等于 50 电池等于 500 金瓜子）的盲盒 10 个（预制的 10 个，不是手动点击 10 个）时，
           * 如果抽到了 2 个「价值」 9.9 元的「冲鸭」礼物，此字段将显示为 9.9 * 2 * 1000 = 19800
           */
          combo_total_coin: number
          crit_prob: number
          demarcation: number
          /**
           * 通常情况下与 `price` 一致
           */
          discount_price: number
          dmscore: number
          draw: number
          effect: number
          effect_block: number
          /**
           * 礼物会返回头像，直接使用以节约流量
           */
          face: string
          face_effect_id: number
          face_effect_type: number
          float_sc_resource_id: number
          giftId: number
          giftName: string
          /**
           * 未知，先预留
           */
          giftType: number
          /**
           * Mar 26, 2025 发现，不知道什么时候加的😅
           */
          gift_info: {
            /**
             * 为 0 时代表没有特效
             * @example 0
             */
            effect_id: 0
            /**
             * @example https://i0.hdslb.com/bfs/live/c806ee29394aab4877fa3d535daca5c66c631306.gif
             */
            gif: string
            /**
             * 通常为 0
             * @example 0
             */
            has_imaged_gift: number
            /**
             * @example https://s1.hdslb.com/bfs/live/8b40d0470890e7d573995383af8a8ae074d485d9.png
             */
            img_basic: string
            /**
             * @example https://i0.hdslb.com/bfs/live/28357ba4cd566418730ca29da2c552efa7e4a390.webp
             */
            webp: string
          }
          /**
           * @example [0]
           */
          gift_tag: number[]
          gold: number
          /**
           * 0: 白字
           * 1: 总督
           * 2: 提督
           * 3: 舰长
           */
          guard_level: number
          is_first: boolean
          is_join_receiver: boolean
          /**
           * 冠名礼物？待调查
           */
          is_naming: boolean
          is_special_batch: number
          magnification: number
          /**
           * 与 SC 不同，礼物事件中如果发送者没有佩戴粉丝勋章，所有 `medal_info` 中的字段都会返回 0。
           * SC 事件中 `medal_info` 则直接返回 `null`
           */
          medal_info: FansMedalItem | 0
          name_color: string
          num: number
          original_gift_name: string
          /**
           * 礼物单价（金瓜子），此处为礼物单价，如果是盲盒，则是抽出礼物的虚拟价值
           *
           * 例如用户赠送 价值 5 元（等于 50 电池等于 500 金瓜子）的盲盒 10 个（预制的 10 个，不是手动点击 10 个）时，
           * 如果抽到了 2 个「价值」 9.9 元的「冲鸭」礼物，此字段将显示为 9900
           */
          price: number
          rcost: number
          /**
           * 当前礼物的接收方，多人连麦时会与当前主播不同，可从 `bizSource` 进行判断
           */
          receive_user_info: {
            uid: number
            uname: string
          }
          receiver_uinfo: UINFO
          /**
           * 当前房间如果开启多人定向投喂（如冰火活动）时，`send_master` 为对象，否则为 `null`
           * 比如冰火歌会时投喂给奶绿此处对象为奶绿，主要用于暴露此对象用中的 `room_id` 用来判断是否时送给特定主播
           */
          send_master: {
            room_id: number
            uid: number
            uname: string
          } | null
          sender_uinfo: UINFO
          /**
           * 被投喂礼物在当前用户包裹中剩余多少
           * 0 可能代表非包裹礼物（需购买礼物）
           */
          remain: number
          rnd: string
          silver: number
          super: number
          super_batch_gift_num: number
          super_gift_num: number
          svga_block: number
          switch: boolean
          tag_image: string
          /**
           * 与 rnd 一样返回一个随机字符串. ie "2186127075633541120"。并不是 unix timestamp
           */
          tid: string
          /**
           * 时间戳 1692802715
           */
          timestamp: number
          top_list: null
          /**
           * 包含了折扣（大航海折扣，例如小电视飞船）后的礼物总价（单价 * 数量）。
           * 该字段为用户送礼所消耗的实际金瓜子数量（就是说如果送的是盲盒，此处为盲盒本身价格）
           *
           * 例如用户赠送 价值 5 元（等于 50 电池等于 500 金瓜子）的盲盒 10 个（预制的 10 个，不是手动点击 10 个）时，
           * 如果抽到了 2 个「价值」 9.9 元的「冲鸭」礼物，此字段将显示为盲盒价格 5 * 2 * 1000 = 10000
           */
          total_coin: number
          uid: number
          uname: string
          /**
           * 荣耀等级
           */
          wealth_level: number
        }
      }

      /**
       * 礼物事件 V2
       *
       * 基于 protobuf 协议，只有某些特定的直播间内会出现
       *
       * 测试直播间 https://live.bilibili.com/1883358196
       *
       * @since 发现于 Apr 24, 2025，可能出现的更早
       */
      export interface SEND_GIFT_V2 {
        cmd: 'SEND_GIFT_V2'
        /**
         * 未知
         */
        danmu: {
          /** 未知，通常为 1 */
          area: number
        }
        data: {
          /** 未知，通常不为 0 */
          dmscore: number
          pb: string
        }
      }

      /** Item of `INTERACT_WORD` */
      export interface FansMedalItem {
        /**
         * 直播间号，在点赞事件中返回 0，其他类型事件需要更多确认
         */
        anchor_roomid: number
        /**
         * 在点赞事件中不存在
         */
        anchor_uname?: string
        guard_level: number
        icon_id: number
        is_lighted: number
        medal_color: number
        medal_color_border: number
        medal_color_end: number
        medal_color_start: number
        medal_level: number
        medal_name: string
        special: string
        /** UID */
        target_id: number
        /**
         * 未知，点赞事件中存在，例如 27000
         */
        score?: number
      }

      /**
       * 互动事件 V2
       *
       * 基于 protobuf 协议，只有某些特定的直播间内会出现
       *
       * @since Jul 2, 2025
       */
      export interface INTERACT_WORD_V2 {
        cmd: 'INTERACT_WORD_V2'
        data: {
          dmscore: number
          /**
           * @example `CIqA0wQSD+eIhueCuOmjnuacuuWktCIBASgBMPj69ws4z8KfwwZAxL2grv0yYgB47fygnMm5xKcYmgEAsgFoCIqA0wQSXQoP54iG54K46aOe5py65aS0EkpodHRwczovL2kyLmhkc2xiLmNvbS9iZnMvZmFjZS8xYzQ0MmY2ZGU2MjBhMThhYjllZDY0YjJmNjljYWM3ZTMzMjhkMTAwLmpwZyIAMgC6AQA=`
           */
          pb: string
        }
      }

      /**
       * 醒目留言
       */
      export interface SUPER_CHAT_MESSAGE {
        cmd: 'SUPER_CHAT_MESSAGE'
        roomid: number
        /** 默认为 true，不知道为啥 */
        is_report: boolean
        /** `"25633924938475008:1000:1000"` */
        msg_id: string
        /** 未知，通常为 true */
        p_is_ack: boolean
        /** 未知，通常为 1 */
        p_msg_type: number
        /** 1737446711685，比 data.ts/start_time 精度高 */
        send_time: number
        data: {
          background_bottom_color: string
          background_color: string
          background_color_end: string
          background_color_start: string
          background_icon: string
          /**
           * 通常为荣耀等级所对应的角标
           * @link https://i0.hdslb.com/bfs/live/8552fe0bbcdea2bc1c72911c015f858f4b32f7f7.png
           * */
          background_image: string
          background_price_color: string
          color_point: number
          /**
           * 一种事件风控分数标准，通常为整数，在2024 8-9月的某一天，b站支持显示自己sc的审核状态后，当前登录状态的 ws 会显示两个 sc 事件，
           * 一个是正常发出的状态，一个是不包含 `dmscore` 的状态，可通过这个状态来判断是否是公开的 sc
           */
          dmscore?: number
          end_time: number
          gift: {
            gift_id: number
            /** `"醒目留言"` */
            gift_name: string
            num: number
          }
          /** 粉丝团套票信息 */
          group_medal: {
            /** 是否点亮 */
            is_lighted: number
            /** 粉丝团 ID，不存在时为 0 */
            medal_id: number
            /** 粉丝牌名称，不存在时返回空 */
            name: string
          }
          id: number
          /** 是否是神秘人 */
          is_mystery: boolean
          is_ranked: number
          /**
           * 是否是被审核过的 SC
           *
           * - 0: 无审核
           * - 1: 已审核
           */
          is_send_audit: number
          medal_info: FansMedalItem | null
          message: string
          /** Message color in HEX format */
          message_font_color: string
          /**
           * SC 翻译正文
           *
           * 用户勾选翻译的话可用，目前为日文
           *
           * 如果直播间见开启了 SC 全局审核，那么所有的 SC 都会自动附上翻译，需要通过 `transMark` 判断
           * 是否真的开启了 SC 翻译
           */
          message_trans: string
          price: number
          /** 未知，默认为 1000，可能和折扣有关 */
          rate: number
          start_time: number
          /** SC 展示时常，单位为秒 */
          time: number
          /**
           * 举报 SC 时会用到，ie "98400C5A"
           */
          token: string
          /**
           * 是否开启翻译
           *
           * - 0: 未开启翻译
           * - 1: 已开启翻译
           */
          trans_mark: number
          ts: number
          uid: number
          uinfo: UINFO
          user_info: {
            face: string
            face_frame: string
            guard_level: number
            /** 主站大会员？ */
            is_main_vip: number
            /** 主站超级大会员？ */
            is_svip: number
            is_vip: number
            /** "#ff9f3d" */
            level_color: string
            manager: number
            /** "#FF7C28" */
            name_color: string
            title: string
            uname: string
            /** UL 用户等级 */
            user_level: number
          }
        }
      }

      /**
       * 醒目留言被删除
       */
      export interface SUPER_CHAT_MESSAGE_DELETE {
        cmd: 'SUPER_CHAT_MESSAGE_DELETE'
        data: {
          /**
           * 被删除的 SC ID，对应 `SuperChat` 中的 `scId`（`gift_id`）
           */
          ids: number[]
        }
        roomid: number
      }

      /**
       * MVP，守护系列事件
       */
      export interface USER_VIRTUAL_MVP {
        cmd: 'USER_VIRTUAL_MVP'
        data: {
          /**
           * 文案：解锁
           */
          action: string
          animation_block: number
          effect_id: number
          effect_queue: number
          /**
           * 礼物图标
           */
          goods_icon: string
          goods_id: number
          /**
           * 文案：守护圣法师x7天
           */
          goods_name: string
          /**
           * 礼物数量
           */
          goods_num: number
          /**
           * 礼物价格，货币价格需要 / 1000
           */
          goods_price: number
          order_id: number
          success_toast: string
          timestamp: number
          uid: number
          uname: string
          uname_color: string
          /**
           * 当前主播的大航海等级
           */
          user_guard_level: number
        }
      }

      /**
       * 点赞事件，具体的某个用户的点赞行为，只有开播的时候点赞才会触发
       */
      export interface LIKE_INFO_V3_CLICK {
        cmd: 'LIKE_INFO_V3_CLICK'
        data: {
          show_area: number
          msg_type: number
          like_icon: string
          uid: number
          /**
           * 文案：为主播点赞了
           */
          like_text: string
          uname: string
          uname_color: string
          /** 未知，待调查 */
          identities: number[]
          /** 用户当前佩戴的粉丝勋章/粉丝勋章 */
          fans_medal: FansMedalItem
          contribution_info: {
            grade: number
          }
          dmscore: number
          group_medal: null
          is_mystery: boolean
          uinfo: UINFO
        }
      }

      /**
       * 红包发送事件
       * @deprecated Use `POPULARITY_RED_POCKET_V2_START`
       */
      export interface POPULARITY_RED_POCKET_START {
        cmd: 'POPULARITY_RED_POCKET_START'
        data: {
          /**
           * 红包唯一识别 ID
           */
          lot_id: number
          sender_uid: number
          sender_name: string
          sender_face: string
          join_requirement: number
          danmu: string
          /**
           * 红包发送时间
           */
          current_time: number
          /**
           * 红包开始时间，因为会排队，所以可能会和 `current_time` 不同
           */
          start_time: number
          end_time: number
          last_time: number
          remove_time: number
          replace_time: number
          lot_status: number
          h5_url: string
          user_status: number
          awards: RedEnvelopeRewardsItem[]
          lot_config_id: number
          /**
           * 该值为金瓜子价格，对应抽奖的金额，为发红包用户发放金额的 80%（20% 归主播）
           */
          total_price: number
          wait_num: number
        }
      }

      /** Item of `POPULARITY_RED_POCKET_START` */
      interface RedEnvelopeRewardsItem {
        gift_id: number
        gift_name: string
        gift_pic: string
        num: number
      }

      /**
       * 红包发送事件v2，支持上舰红包
       */
      export interface POPULARITY_RED_POCKET_V2_START {
        cmd: 'POPULARITY_RED_POCKET_V2_START'
        data: {
          /**
           * 红包唯一识别 ID
           */
          lot_id: number
          sender_uid: number
          sender_name: string
          sender_face: string
          join_requirement: number
          danmu: string
          /**
           * 红包发送时间
           */
          current_time: number
          /**
           * 红包开始时间，因为会排队，所以可能会和 `current_time` 不同
           */
          start_time: number
          end_time: number
          last_time: number
          remove_time: number
          replace_time: number
          lot_status: number
          h5_url: string
          user_status: number
          /**
           * 礼物列表
           */
          awards: RedEnvelopeRewardsItem[]
          lot_config_id: number
          /**
           * - 当发放的是礼物红包时：该值为礼物的金瓜子价格，对应抽奖的金额，为发红包用户发放金额的 80%（20% 归主播）。例如用户发 20 电池（2000 金瓜子）的礼物时，此处会返回 1600 金瓜子
           * - 当发放的是舰长红包时：此处为红包本身的价格
           */
          total_price: number
          wait_num: number

          // 以下为 V2 新增
          wait_num_v2: number
          /**
           * 是否为神秘人
           */
          is_mystery: boolean
          rp_type: number
          sender_uinfo: UINFO
          /**
           * V2 新增的红包图标，礼物红包时为 `''`
           */
          icon_url: string
          /**
           * V2 新增的红包图标，礼物红包时为 `''`
           */
          animation_icon_url: string
        }
      }

      /**
       * 红包中奖结果
       * @deprecated Use `POPULARITY_RED_POCKET_V2_WINNER_LIST`
       */
      export interface POPULARITY_RED_POCKET_WINNER_LIST {
        cmd: 'POPULARITY_RED_POCKET_WINNER_LIST'
        data: {
          lot_id: number
          total_num: number
          /**
           * 中奖名单
           */
          winner_info: RedEnvelopeResultWinnerItem[]
          /**
           * 礼物详情
           */
          awards: {
            [key: string]: RedEnvelopeResultRewardsItem
          }
          version: number
        }
      }

      /** Item of `POPULARITY_RED_POCKET_WINNER_LIST` */
      type RedEnvelopeResultWinnerItem = [
        /**
         * 用户 UID
         */
        number,
        /**
         * 用户昵称
         */
        string,
        /**
         * 未知，疑似中奖唯一识别符
         */
        number,
        /**
         * 中奖所获得的礼物 ID
         */
        number,
      ]

      /** Item of `POPULARITY_RED_POCKET_WINNER_LIST` */
      interface RedEnvelopeResultRewardsItem {
        /**
         * 已知的：
         *
         * - 虚拟礼物：`1`
         * - 上舰红包：`5`
         */
        award_type: number
        award_name: string
        award_pic: string
        /**
         * 可能为空
         */
        award_big_pic: string
        award_price: number
      }

      /**
       * 红包中奖结果
       */
      export interface POPULARITY_RED_POCKET_V2_WINNER_LIST {
        cmd: 'POPULARITY_RED_POCKET_V2_WINNER_LIST'
        data: {
          lot_id: number
          total_num: number
          /**
           * 上舰红包此处为0，不知道为啥😅
           */
          award_num: 0
          /**
           * 中奖名单
           */
          winner_info: RedEnvelopeResultV2WinnerItem[]
          /**
           * 礼物详情
           */
          awards: {
            [key: string]: RedEnvelopeResultRewardsItem
          }
          version: number
          rp_type: 2
          /**
           * 😅
           */
          timestamp: 0
        }
      }

      /** Item of `POPULARITY_RED_POCKET_V2_WINNER_LIST` */
      type RedEnvelopeResultV2WinnerItem = [
        /**
         * 0. 用户 UID
         */
        number,
        /**
         * 1. 用户昵称
         */
        string,
        /**
         * 2. 未知，通常为 0
         */
        number,
        /**
         * 3. 未知，通常为 0
         */
        number,
        /**
         * 4. 未知，通常为 false
         */
        boolean,
        {
          /**
           * 未知
           */
          channel: 8
          /**
           * 未知
           */
          op_type: 1
          /**
           *  未知
           */
          payTxt: 0
          /**
           * 未知
           */
          isGuardWelfare: 2
        },
        /**
         * 6. 中奖时间戳
         */
        number,
        /**
         * 7. 对应主播直播间
         */
        number,
      ]

      /**
       * 天选时刻开始事件
       */
      export interface ANCHOR_LOT_START {
        cmd: 'ANCHOR_LOT_START'
        data: {
          asset_icon: string
          asset_icon_webp: string
          award_image: string
          /**
           * 礼物名称，由主播定义
           */
          award_name: string
          award_num: number
          /**
           * `"价值52电池"`
           */
          award_price_text?: string
          /**
           * 未知，通常为 `1`
           */
          award_type: number
          cur_gift_num: number
          current_time: number
          /**
           * 需要用户发送的弹幕
           */
          danmu: string
          danmu_new: {
            danmu: string
            /**
             * 未知，通常为空字符串
             */
            danmu_view: string
            /**
             * 未知，通常为 `false`
             */
            reject: boolean
          }[]
          gift_id: number
          /**
           * 需要用户赠送的礼物名称
           */
          gift_name: string
          gift_num: number
          gift_price: number
          goaway_time: number
          goods_id: number
          /**
           * 天选唯一识别 ID
           */
          id: number
          is_broadcast: number
          join_type: number
          lot_status: number
          /**
           * 天选持续时间
           */
          max_time: number
          /**
           * 天选资格设定
           */
          require_text: string
          require_type: number
          require_value: number
          room_id: number
          send_gift_ensure: number
          show_panel: number
          start_dont_popup: number
          status: number
          time: number
          url: string
          web_url: string
        }
      }

      /**
       * 天选时刻中奖结果
       */
      export interface ANCHOR_LOT_AWARD {
        cmd: 'ANCHOR_LOT_AWARD'
        data: {
          /**
           * 未中奖时不会弹出，为 `1`，中奖时为 `0`
           */
          award_dont_popup: 1 | 0
          award_image: string
          award_name: string
          award_num: number
          award_users: Array<{
            uid: number
            uname: string
            face: string
            /**
             * 粉丝勋章等级
             */
            level: number
            color: number
          }>
          /**
           * 天选唯一识别 ID
           */
          id: number
          lot_status: number
          url: string
          web_url: string
        }
      }

      /** Text segment in notice event */
      export interface NoticeSegmentText {
        /**
         * - 1: 普通文本
         * - 3: 带有 `uri` 的超链接按钮
         */
        type: 1 | 3
        /** 是否为粗体 */
        font_bold?: boolean
        /** 字体颜色，例如：'#FF7373' */
        font_color: string | null
        /** 字体颜色深色模式 */
        font_color_dark?: string | null
        /** 高亮字体颜色
         *
         * 只有当 segment 中含有 `<%` 与 `%>` 标记时才会出现，例如：
         *
         * `恭喜用户 username <%荣耀等级升级至32级%>`
         */
        highlight_font_color?: string | null
        /** 高亮字体颜色的深色模式 */
        highlight_font_color_dark?: string | null
        /** 背景颜色，可以是 string or array 😅 */
        background_color?: string[] | string | null
        /** 背景颜色深色模式 */
        background_color_dark?: string[] | string | null
        img_height?: number
        img_width?: number
        img_url?: string
        /** text 同样可以不存在😅，有的时候你b会只返回 type 和 font_color，不带任何内容，真傻逼啊 */
        text?: string
        /** 超链接按钮的 URL，`type` 为 3 时才会出现 */
        uri?: string
      }

      /** Image segment in notice event */
      export interface NoticeSegmentImage {
        type: 2
        img_height: number
        img_width: number
        img_url: string
      }

      /** 通用通知事件中的具体片段 */
      export type NoticeSegment = NoticeSegmentText | NoticeSegmentImage

      /**
       * 通用通知事件
       * 可以是大乱斗、礼物公告等各种通知：
       *
       * 礼物通告：
       *
       * ```json
       * [{"font_color":"#FB7299","text":"xxx在元气赏中五连抽！送出了好多礼物！","type":1}]
       * ```
       *
       * 干杯之旅礼物，Nov 22, 2024 左右添加的礼物事件😅
       *
       * ```json
       * [
       *   {
       *     type: 1,
       *     font_color: '#FF7373',
       *     text: 'スパラノイア',
       *   },
       *   {
       *     type: 1,
       *     font_color: '#99A5AE',
       *     text: '投喂',
       *   },
       *   {
       *     type: 1,
       *     font_color: '#FF7373',
       *     text: '干杯之旅',
       *   },
       * ]
       * ```
       */
      export interface COMMON_NOTICE_DANMAKU {
        cmd: 'COMMON_NOTICE_DANMAKU'
        data: {
          /** 作用未知
           * 通常为 `[1, 2, 3, 4, 5]`
           */
          terminals: number[]
          content_segments: NoticeSegment[]
        }
      }

      /**
       * 特效弹幕（例如春节时的 万福贺岁）
       * 此事件用来触发弹幕聊天内容显示
       */
      export interface EFFECT_DANMAKU_MSG {
        cmd: 'EFFECT_DANMAKU_MSG'
        data: {
          dmscore: number
          goods_info: {
            /** 投喂 */
            action: string
            /** https://s1.hdslb.com/bfs/live/815222655ef0fbec9fd1c3b6fa4d34ec91d916dd.png */
            img: string
            /** 万福贺岁 */
            name: string
            /** x1 */
            num: string
            /** 并且留言： */
            prefix: string
            /** test content */
            text: string
          }
          sender_uinfo: BilibiliInternal.WebSocket.Prod.UINFO
        }

        /**
         * 唯一 ID, ie. `"25968668409605632:1000:1000"`
         */
        msg_id: string

        /**
         * 未知，通常为 true
         */
        p_is_ack: boolean

        /**
         * 未知，通常为 1
         */
        p_msg_type: number

        /** 发送时间，1738085184187 */
        send_time: number
      }

      /**
       * 投喂例如 万福贺岁 礼物时会同时触发 EFFECT_DANMAKU_MSG 和 DANMU_ACTIVITY_CONFIG
       * 此事件用来触发特效
       */
      export interface DANMU_ACTIVITY_CONFIG {
        cmd: 'DANMU_ACTIVITY_CONFIG'
        data: {
          /** 通常为 3017 */
          dm_mode: number
          /** 通常为 1 */
          dm_setting_switch: number
          /** 通常为 1738085194 */
          etime: number
          /** 通常为 '' */
          extra: string
          /** 通常为 15964066 */
          id: number
          material_conf: {
            /** 通常为 'http://i0.hdslb.com/bfs/live/4ded8c3d4f9252e74f920106ba48af26ac166878.zip' */
            activity_test_material: string
            /** 通常为 0 */
            activity_type: number
            /** 通常为 '#EABB6A' */
            background_color: string
            /** 通常为 'http://i0.hdslb.com/bfs/live/4ded8c3d4f9252e74f920106ba48af26ac166878.zip' */
            backup_url: string
            /** 通常为 15 */
            main_head_fps: number
            /** 通常为 '#F6FFCC' */
            main_state_dm_color: string
            /** 通常为 '#DF9432' */
            main_stroke_color: string
            /** 通常为 15 */
            main_tail_fps: number
            /** 通常为 [] */
            material_mode: number[]
            mod_resource: {
              /** 通常为 'special_dm_v2_1110011' */
              mobi_module: string
              /** 通常为 1 */
              mobi_module_version: number
              /** 通常为 'live' */
              mobi_pool: string
            }
            /** 通常为 15 */
            objective_head_fps: number
            /** 通常为 '#FFF8CC' */
            objective_state_dm_color: string
            /** 通常为 '#E58544' */
            objective_stroke_color: string
            /** 通常为 0 */
            objective_tail_fps: number
            /** 通常为 '' */
            web_material: string
          }
          mock_options: {
            /** 通常为 `'{"animation":null,"content":"test","user_name":"スパラノイア","user_name_color":"#FFEAB2","user_name_stroke_color":"#C16223"}'` */
            dm_extra: string
            /** 通常为 1738085244 */
            trigger_time: number
          }[]

          /** 通常为 [3, 2, 1, 4] */
          platform: number[]
          /** 通常为 3 */
          screen_type: number
          /** 通常为 1 */
          source: number
          /** 通常为 1 */
          status: number
          /** 通常为 1738085184 */
          stime: number
          /** 通常为 '1:2501290126241262027634302' */
          unique_id: string
        }
        /** 通常为 '25968668391255552:1000:1000' */
        msg_id: string
        /** 通常为 true */
        p_is_ack: boolean
        /** 通常为 1 */
        p_msg_type: number
        /** 通常为 1738085184152 */
        send_time: number
      }

      /**
       * 直播间标题变更
       */
      export interface ROOM_CHANGE {
        cmd: 'ROOM_CHANGE'
        data: {
          title: string
          area_id: number
          parent_area_id: number
          /**
           * ie. 社科法律心理
           */
          area_name: string
          /**
           * ie. 知识
           */
          parent_area_name: string
          /**
           * 默认返回 '0'
           */
          live_key: string
          /**
           * 默认返回 ''
           */
          sub_session_key: string
        }
      }

      /**
       * 开启直播间禁言
       */
      export interface ROOM_SILENT_ON {
        cmd: 'ROOM_SILENT_ON'
        data: {
          /**
           * 禁言类型：
           * - `level` 用户等级（已退出历史舞台）
           * - `wealth` 荣耀等级
           * - `medal` 粉丝勋章等级
           * - `member` 全员
           */
          type: 'level' | 'wealth' | 'medal' | 'member'
          level: number
          /**
           * 禁言时长，`-1` 时为永久禁言，unix 时间戳时为限时禁言
           */
          second: number
          /**
           * ie. '[系统]: 主播对荣耀等级1级以下的用户开启了禁言'
           */
          msg: string
        }
      }

      /**
       * 关闭直播间禁言
       */
      export interface ROOM_SILENT_OFF {
        cmd: 'ROOM_SILENT_OFF'
        data: {
          /** 通常为空 */
          type: string
          /** 通常为 0 */
          level: number
          /** 通常为 0 */
          second: number
          /** `'[系统]: 主播取消了房间禁言'` */
          msg: string
        }
      }

      /**
       * 直播间观看人数变更
       */
      export interface WATCHED_CHANGE {
        cmd: 'WATCHED_CHANGE'
        data: {
          num: number
          /**
           * '2992'
           */
          text_small: string
          /**
           * '2992人看过'
           */
          text_large: string
        }
      }

      /**
       * 点赞人数变更，总的点赞更新
       */
      export interface LIKE_INFO_V3_UPDATE {
        cmd: 'LIKE_INFO_V3_UPDATE'
        data: {
          click_count: number
        }
      }

      /**
       * 高能榜变更
       */
      export interface ONLINE_RANK_COUNT {
        cmd: 'ONLINE_RANK_COUNT'
        data: {
          /**
           * 应该是老版高能榜，数值低于 `online_count`
           */
          count: number
          /** `count` 对应的字符串 */
          count_text: string
          /**
           * 新版高能榜，但傻逼b站并不是每次都会返回，会隔一秒返回一次，因此可能为 undefined 😅
           */
          online_count?: number | undefined
          /** `online_count_text` 对应的字符串 */
          online_count_text?: string | undefined
        }
      }

      /**
       * 高能用户列表
       */
      export interface ONLINE_RANK_V2 {
        cmd: 'ONLINE_RANK_V2'
        data: {
          online_list: {
            uid: number
            face: string
            /** 打赏电池数 */
            score: string
            uname: string
            /** 当前排名 */
            rank: number
            guard_level?: number
            is_mystery?: boolean
            uinfo: UINFO
          }[]
          rank_type: string
        }
      }

      /**
       * 高能用户列表 v3
       * @since Jul 5, 2025
       */
      export interface ONLINE_RANK_V3 {
        cmd: 'ONLINE_RANK_V3'
        data: {
          /**
           * @example `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`
           */
          pb: string
        }
      }

      /**
       * 直播间基本信息更新，可用于获取实时粉丝数、粉丝团人数
       */
      export interface ROOM_REAL_TIME_MESSAGE_UPDATE {
        cmd: 'ROOM_REAL_TIME_MESSAGE_UPDATE'
        data: {
          /** 直播间号 */
          roomid: number
          /** 粉丝数 */
          fans: number
          /** 未知，通常为 -1 */
          red_notice: number
          /** 粉丝团人数 */
          fans_club: number
        }
      }

      /**
       * 直播间实时基本信息更新
       *
       * 以前出现过的信息，曾经不再推送此消息，Jul 5, 2025 发现又开始推了😅
       *
       * @since Jul 5, 2025
       */
      export interface ROOM_REAL_TIME_MESSAGE_UPDATE_V2 {
        cmd: 'ROOM_REAL_TIME_MESSAGE_UPDATE_V2'
        data: {
          /** 粉丝数 */
          fans: number
          /** 粉丝团人数 */
          fans_club: number
          /** 未知，通常为 -1 */
          red_notice: number
          /** 直播间号 */
          roomid: number
        }
      }

      /**
       * 开播事件
       */
      export interface LIVE {
        cmd: 'LIVE'
        live_key: string
        voice_background: string
        sub_session_key: string
        /** 开播平台：例如 pc */
        live_platform: string
        /** 未知，通常为 0 */
        live_model: number
        roomid: number
        /** 开播 unix 时间戳，例如 `1718132706` */
        live_time: number
      }

      /**
       * 直播间准备事件，触发此事件时通常代表主播已下播
       */
      export interface PREPARING {
        cmd: 'PREPARING'
        /** 直播间房间号的字符串形式😅 */
        roomid: string
      }

      /**
       * 直播间被警告
       */
      export interface WARNING {
        cmd: 'WARNING'
        /** 图片内容不适宜，请立即调整 */
        msg: string
        roomid: number
      }

      /**
       * 直播间被切断
       */
      export interface CUT_OFF {
        cmd: 'CUT_OFF'
        /** 违反直播言论规范，请立即调整 */
        msg: string
        roomid: number
      }

      /**
       * 指定观众禁言
       */
      export interface ROOM_BLOCK_MSG {
        cmd: 'ROOM_BLOCK_MSG'
        data: {
          dmscore: number
          /**
           * 操作人，封禁操作由谁发起
           *
           * 1: 房管
           * 2: 主播
           */
          operator: 2
          uid: number
          uname: string
          /**
           * 封禁过期时间戳，后加的，防止之后再删除此处定义为 optional
           * ie: `1721921788`
           */
          block_expired?: number
          /**
           * 禁言时长文案，给b站前端展示用
           * - `2小时有效`
           */
          vaild_period?: string
        }
        /** 字符串格式 uid */
        uid: string
        uname: string
      }

      /**
       * 房管列表，只有在新增房管 `room_admin_entrance` 时会触发，用于让前端给房管用户增加标记
       */
      export interface ROOM_ADMINS {
        cmd: 'ROOM_ADMINS'
        uids: number[]
      }

      /**
       * 新增房管
       */
      export interface room_admin_entrance {
        cmd: 'room_admin_entrance'
        dmscore: number
        /** 未知等级 */
        level: number
        /** `系统提示：你已被主播设为房管` */
        msg: string
        uid: number
      }

      /**
       * 撤销房管
       */
      export interface ROOM_ADMIN_REVOKE {
        cmd: 'ROOM_ADMIN_REVOKE'
        /** `"撤销房管"` */
        msg: string
        uid: number
      }
    }

    /**
     * 开放平台
     * @link https://open-live.bilibili.com/document/f9ce25be-312e-1f4a-85fd-fef21f1637f8
     */
    namespace OpenPlatform {
      /**
       * 弹幕事件
       */
      export interface LIVE_OPEN_PLATFORM_DM {
        /** 命令类型 */
        cmd: 'LIVE_OPEN_PLATFORM_DM'
        data: {
          /** 弹幕接收的直播间 */
          room_id: number
          /** 用户UID(即将废弃) */
          uid: number
          /** 用户唯一标识(2024-03-11后上线) */
          open_id: string
          /** 用户昵称 */
          uname: string
          /** 弹幕内容 */
          msg: string
          /** 消息唯一id */
          msg_id: string
          /** 对应房间勋章信息 */
          fans_medal_level: number
          /** 粉丝勋章名 */
          fans_medal_name: string
          /** 该房间粉丝勋章佩戴情况 */
          fans_medal_wearing_status: boolean
          /** 对应房间大航海 1总督 2提督 3舰长 */
          guard_level: number
          /** 弹幕发送时间秒级时间戳 */
          timestamp: number
          /** 用户头像 */
          uface: string
          /** 表情包图片地址，无表情包时返回空字符串 */
          emoji_img_url: string
          /** 弹幕类型 0：普通弹幕 1：表情包弹幕 */
          dm_type: number
          /**
           * 直播荣耀等级
           * Jan 19, 2025 新增
           */
          glory_level: number
          /**
           * 被at用户唯一标识
           * Jan 19, 2025 新增
           */
          reply_open_id: string
          /**
           * 被at的用户昵称
           * Jan 19, 2025 新增
           */
          reply_uname: string
          /**
           * 发送弹幕的用户是否是房管，取值范围0或1，取值为1时是房管
           * Jan 19, 2025 新增
           */
          is_admin: boolean
        }
      }

      /**
       * 礼物事件
       */
      export interface LIVE_OPEN_PLATFORM_SEND_GIFT {
        cmd: 'LIVE_OPEN_PLATFORM_SEND_GIFT'
        data: {
          /** 直播间(演播厅模式则为演播厅直播间,非演播厅模式则为收礼直播间) */
          room_id: number
          /** 用户UID（已废弃，固定为0） */
          uid: number
          /** 用户唯一标识(2024-03-11后上线) */
          open_id: string
          /** 送礼用户昵称 */
          uname: string
          /** 送礼用户头像 */
          uface: string
          /** 道具id(盲盒:爆出道具id) */
          gift_id: number
          /** 道具名(盲盒:爆出道具名) */
          gift_name: string
          /** 赠送道具数量 */
          gift_num: number
          /** 礼物单价(1000 = 1元 = 10电池),盲盒:爆出道具的价值 */
          price: number
          /** 实际价值(1000 = 1元 = 10电池),盲盒:爆出道具的价值，Nov 26, 2024 添加 */
          r_price: number
          /** 是否是付费道具 */
          paid: boolean
          /** 实际收礼人的勋章信息 */
          fans_medal_level: number
          /** 粉丝勋章名 */
          fans_medal_name: string
          /** 该房间粉丝勋章佩戴情况 */
          fans_medal_wearing_status: boolean
          /** room_id对应的大航海等级 */
          guard_level: number
          /** 收礼时间秒级时间戳 */
          timestamp: number
          /** 消息唯一id */
          msg_id: string
          anchor_info: {
            /** 收礼主播uid */
            uid: number
            /** 收礼主播唯一标识(2024-03-11后上线) */
            open_id: string
            /** 收礼主播昵称 */
            uname: string
            /** 收礼主播头像 */
            uface: string
          }
          /** 道具icon */
          gift_icon: string
          /** 是否是combo道具 */
          combo_gift: boolean
          /** 连击信息 */
          combo_info: {
            /** 每次连击赠送的道具数量 */
            combo_base_num: number
            /** 连击次数 */
            combo_count: number
            /** 连击id */
            combo_id: string
            /** 连击有效期秒 */
            combo_timeout: number
          }
          /**
           * 盲盒信息
           *
           * 2025-05-21 新增
           */
          blind_gift: {
            /** 是否是盲盒 */
            status: true
            /** 盲盒id */
            blind_gift_id: number
          }
        }
      }

      /**
       * 醒目留言事件
       */
      export interface LIVE_OPEN_PLATFORM_SUPER_CHAT {
        cmd: 'LIVE_OPEN_PLATFORM_SUPER_CHAT'
        data: {
          /**
           * 直播间id
           */
          room_id: number
          /** 用户UID(即将废弃) */
          uid: number
          /** 用户唯一标识(2024-03-11后上线) */
          open_id: string
          /**
           * 购买的用户昵称
           */
          uname: string
          /**
           * 购买用户头像
           */
          uface: string
          /**
           * 留言id(风控场景下撤回留言需要)
           */
          message_id: number
          /**
           * 留言内容
           */
          message: string
          /**
           * 消息唯一id
           */
          msg_id: string
          /**
           * 支付金额(元)
           */
          rmb: number
          /**
           * 赠送时间秒级
           */
          timestamp: number
          /**
           * 生效开始时间
           */
          start_time: number
          /**
           * 生效结束时间
           */
          end_time: number
          /**
           * 对应房间大航海等级 (新增)
           */
          guard_level: number
          /**
           * 对应房间勋章信息 (新增)
           */
          fans_medal_level: number
          /**
           * 对应房间勋章名字 (新增)
           */
          fans_medal_name: string
          /**
           * 该房间粉丝勋章佩戴情况 (新增)
           */
          fans_medal_wearing_status: boolean
        }
      }

      /**
       * 大航海事件
       */
      export interface LIVE_OPEN_PLATFORM_GUARD {
        cmd: 'LIVE_OPEN_PLATFORM_GUARD'
        data: {
          user_info: {
            /** 用户UID(即将废弃) */
            uid: number
            /** 用户唯一标识(2024-03-11后上线) */
            open_id: string
            /**
             * 用户昵称
             */
            uname: string
            /**
             * 用户头像
             */
            uface: string
          }
          /**
           * 大航海价格，之前没有，不知道什么时候加的，连个api changelog都没😅
           * 单位金瓜子，1000金瓜子 = 1元 = 10电池
           */
          price: number
          /**
           * 对应的大航海等级 1总督 2提督 3舰长
           */
          guard_level: 1 | 2 | 3
          /**
           * 大航海数量
           */
          guard_num: number
          /**
           * 大航海单位 (个月)
           */
          guard_unit: string
          /**
           * 粉丝勋章等级
           */
          fans_medal_level: number
          /**
           * 粉丝勋章名
           */
          fans_medal_name: string
          /**
           * 该房间粉丝勋章佩戴情况
           */
          fans_medal_wearing_status: boolean
          /**
           * 时间戳
           */
          timestamp: number
          /**
           * 房间id
           */
          room_id: number
          /**
           * 消息唯一id
           */
          msg_id: string
        }
      }

      /**
       * 点赞事件
       */
      export interface LIVE_OPEN_PLATFORM_LIKE {
        cmd: 'LIVE_OPEN_PLATFORM_LIKE'
        data: {
          /** 用户UID(即将废弃) */
          uid: number
          /** 用户唯一标识(2024-03-11后上线) */
          open_id: string
          /**
           * 用户昵称
           */
          uname: string
          /**
           * 用户头像
           */
          uface: string
          /**
           * 时间戳
           */
          timestamp: number
          /**
           * 点赞文案( "xxx点赞了")
           */
          like_text: string
          /**
           * 对单个用户最近2秒的点赞次数聚合
           */
          like_count: number
          /**
           * 该房间粉丝勋章佩戴情况
           */
          fans_medal_wearing_status: boolean
          /**
           * 对应房间大航海等级 (新增)
           */
          guard_level: number
          /**
           * 粉丝勋章名
           */
          fans_medal_name: string
          /**
           * 粉丝勋章等级
           */
          fans_medal_level: number
          /**
           * 消息唯一id
           */
          msg_id: string
          /**
           * 发生的直播间
           */
          room_id: number
        }
      }

      /**
       * 进入直播间事件
       */
      export interface LIVE_OPEN_PLATFORM_LIVE_ROOM_ENTER {
        cmd: 'LIVE_OPEN_PLATFORM_LIVE_ROOM_ENTER'
        data: {
          /** will always be `0` 😅 */
          uid: number
          /** `1730965563` */
          timestamp: number
          uname: string
          uface: string
          open_id: string
          msg_id: string
          room_id: number
        }
      }

      /**
       * 开播事件
       */
      export interface LIVE_OPEN_PLATFORM_LIVE_START {
        cmd: 'LIVE_OPEN_PLATFORM_LIVE_START'
        data: {
          timestamp: number
          openid: string
          msg_id: string
          room_id: number
          /** 开播分区名, ie. 户外 */
          area_name: string
          /** 开播时刻，直播间的标题 */
          title: string
        }
      }

      /**
       * 下播事件
       */
      export interface LIVE_OPEN_PLATFORM_LIVE_END {
        cmd: 'LIVE_OPEN_PLATFORM_LIVE_END'
        data: {
          /** will always be `0` 😅 */
          uid: number
          timestamp: number
          openid: string
          msg_id: string
          room_id: number
        }
      }

      /**
       * 消息推送结束通知事件
       */
      export interface LIVE_OPEN_PLATFORM_INTERACTION_END {
        cmd: 'LIVE_OPEN_PLATFORM_INTERACTION_END'
        data: {
          game_id: string
          /**
           * @example `1714113037`
           */
          timestamp: number
        }
      }
    }
  }
}

/**
 * vtbs.moe API
 */
export declare namespace Vtbs {
  /**
   * VUP 列表，经过筛选的，并非 vtbs 官方列表，由 vtb 和 vdb 聚合而来
   * @link https://github.com/sparanoid/vup.json
   */
  export interface VupListItem {
    uid: number
    name: string
    type: string
    room: number | null
    face: string
    group_name: string
  }

  /**
   * 根据 vup list 增强后的数据
   */
  export interface EnrichedGlobalGuardItem extends GlobalGuardItem {
    uid: number
    _t1: number
    _t2: number
    _t3: number
    _price_t1: number
    _price_t2: number
    _price_t3: number
    _price_sum: number
    ddEnriched: VupListItem[][]
  }

  /**
   * dd 大航海
   */
  export interface GlobalGuard {
    [key: string]: GlobalGuardItem
  }

  /**
   * dd 大航海 item
   * @link https://api.vtbs.moe/v1/guard/all
   * @link https://api.vtbs.moe/v1/guard/some
   */
  export interface GlobalGuardItem {
    uname: string
    face: string
    mid: number
    dd: [number[], number[], number[]]
  }

  /**
   * 大航海历史 chart
   */
  export interface LiveGuardsHistoryItem {
    guardNum: number
    time: number
  }

  /**
   * 直播间详细信息
   */
  export interface LiveDetails {
    mid: number
    uuid: string
    uname: string
    video: number
    roomid: number
    sign: string
    notice: string
    face: string
    rise: number
    topPhoto: string
    archiveView: number
    follower: number
    liveStatus: number
    recordNum: number
    guardNum: number
    lastLive: {
      online: number
      time: number
    }
    guardChange: number
    /**
     * 总督，提督，舰长
     */
    guardType: [number, number, number]
    online: number
    title: string
    time: number
    liveStartTime: number
  }
}

/**
 * danmakus.com API
 * @link https://ukamnads.icu/swagger
 */
export declare namespace Danmakus {
  /**
   * 直播实时排行榜
   * @link https://ukamnads.icu/api/v2/living
   */
  export interface RankingProps {
    /**
     * 200: success
     */
    code: number
    message: string
    data: RankingItem[]
  }

  /** Item of `RankingProps` */
  export interface RankingItem {
    uId: number
    uName: string
    roomId: number
    faceUrl: string
    frameUrl: string
    isLiving: boolean
    title: string
    tags: string[]
    lastLiveDate: number
    lastLiveDanmakuCount: number
    totalDanmakuCount: number
    totalIncome: {
      value: string
      type: 'Big Number'
    }
    totalLiveCount: number
    totalLiveSecond: number
    addDate: Date
    /** can be empty in some rare case due to some program error */
    livingInfo?: LiveItem
    commentCount: number
    lastLiveIncome: number
  }

  /**
   * 频道信息（直播记录列表）
   * @link https://ukamnads.icu/api/v2/channel?uId=2132180406
   */
  export interface ChannelProps {
    /**
     * 200: success
     */
    code: number
    message: string
    data: ChannelData
  }

  /** Data of `ChannelProps` */
  export interface ChannelData {
    channel: ChannelInfoProps
    lives: LiveItem[]
    extra?: {
      fansHistory: {
        archiveView: number
        follower: number
        time: number
      }[]
      guardHistory: {
        guardNum: number
        areaRank: number
        time: number
      }[]
    }
  }

  /** Props of `ChannelData` */
  export interface ChannelInfoProps {
    uId: number
    uName: string
    roomId: number
    faceUrl: string
    frameUrl: string
    isLiving: boolean
    title: string
    tags: string[]
    lastLiveDate: number
    lastLiveDanmakuCount: number
    totalDanmakuCount: number
    totalIncome: number
    totalLiveCount: number
    totalLiveSecond: number
    addDate: Date
    commentCount: number
    lastLiveIncome: number
  }

  /** General live stream item */
  export interface LiveItem {
    liveId: string
    isFinish: boolean
    isFull: boolean
    parentArea: string
    area: string
    coverUrl: string
    danmakusCount: number
    startDate: number
    stopDate: number
    title: string
    totalIncome: number
    watchCount: number
    likeCount: number
    payCount: number
    interactionCount: number
    onlineRank: number
    maxOnlineCount: number
  }

  /** General danmaku event props */
  export interface DanmakuProps {
    uId: number
    uName: string
    /**
     * - 0: 普通消息
     * - 1: 礼物
     * - 2: 上舰
     * - 3: SuperChat
     * - 4: 进入直播间
     * - 5: 标题变动
     * - 6: 分区变动
     * - 7: 直播中止
     * - 8: 直播继续
     * - 9: 用户封禁
     */
    type: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10
    sendDate: number
    message: string
    price: number
    ct: string
    count: number
  }

  /** Extra props for live item */
  export interface ExtraProps {
    wordCloud: { [key: string]: number }
    onlineRank: { [key: string]: number }
    blockUsers: number[]
  }

  /**
   * 单场直播信息
   * @link https://ukamnads.icu/api/v2/live?liveid=cafc0eb5-a79a-4a0d-b283-3360b6a1a814&includeExtra=true
   */
  export interface LiveProps {
    /**
     * 200: success
     */
    code: number
    message: string
    data: LiveData
  }

  /** Data of `LiveProps` */
  export interface LiveData {
    total: number
    pageNum: number
    pageSize: number
    hasMore: boolean
    data: {
      channel: ChannelInfoProps
      live: {
        liveId: string
        isFinish: boolean
        isFull: boolean
        parentArea: string
        area: string
        coverUrl: string
        danmakusCount: number
        startDate: number
        stopDate: number
        title: string
        totalIncome: number
        watchCount: number
        likeCount: number
        payCount: number
        interactionCount: number
        onlineRank: number
        maxOnlineCount: number
        extra: ExtraProps
      }
      danmakus: DanmakuProps[]
    }
  }

  /**
   * 用户看过的主播
   * @link https://ukamnads.icu/api/v2/user/watchedChannels?uid=2132180406
   */
  export interface WatchedChannels {
    /**
     * 200: success
     */
    code: number
    message: string
    data: WatchedChannelsItem[]
  }

  /** Item of `WatchedChannels` */
  export interface WatchedChannelsItem {
    uId: number
    uName: string
    roomId: number
    faceUrl: string
    frameUrl: string
    isLiving: boolean
    title: string
    tags: string[]
    lastLiveDate: number
    lastLiveDanmakuCount: number
    totalDanmakuCount: number
    totalIncome: number
    totalLiveCount: number
    totalLiveSecond: number
    addDate: Date
    commentCount: number
    lastLiveIncome: number
  }

  /**
   * 用户观看/事件记录
   */
  export interface User {
    code: number
    message: string
    data: UserData
  }

  /** Data of `User` */
  export interface UserData {
    total: number
    pageNum: number
    pageSize: number
    hasMore: boolean
    data: {
      records: UserDataRecordsItem[]
    }
  }

  /** Item of `UserData` */
  export interface UserDataRecordsItem {
    channel: WatchedChannelsItem
    live: LiveItem
    danmakus: DanmakuProps[]
  }
}

/**
 * vup.loveava.top API
 */
export declare namespace LoveAvA {
  /** 直播实时排行 */
  export interface RankingProps {
    /**
     * 0: success
     */
    code: number
    /**
     * `'success'`
     */
    message: string
    data: {
      rooms: RankingItem[]
      ctime: number
    }
  }

  /** Item of `RankingProps` */
  export interface RankingItem {
    roomid: number
    uid: number
    title: string
    uname: string
    system_cover: string
    cover: string
    face: string
    parent_id: number
    parent_name: string
    area_id: number
    area_name: string
    area_v2_parent_id: number
    area_v2_parent_name: string
    area_v2_id: number
    area_v2_name: string
    start_time: number
    /**
     * 热度，danmakus 没有
     */
    count: number
    ten_minutes_counter: number
  }
}

/**
 * Zeroroku API
 */
export declare namespace Zeroroku {
  /**
   * 被知名 UP 关注
   */
  export interface FamousFansItem {
    name: string
    mid: number
    face: string
    fans: number
  }

  /**
   * /fans-history API 中粉丝数项目
   */
  export interface FansItem {
    fans: number
    created_at: string
    date: string
  }
}

/**
 * Aliyun API
 */
export declare namespace Aliyun {
  /**
   * 物流面单信息提取
   * @url https://help.aliyun.com/zh/address-purification/addrpapi/developer-reference/logistics-information-extraction
   */
  export interface ExtractExpress {
    RequestId: string
    /** 正常时返回 */
    Data?: string
    /** 报错时返回 */
    Code?: string
    /** 报错时返回 */
    Message?: string
    /** 报错时返回 */
    HostId?: string
    /** 报错时返回 */
    Recommend?: string
  }

  /**
   * Need JSON.parse() to get from `ExtractExpress.Data`
   */
  export interface ExtractExpressData {
    /** 提取内容，当传入的地址无效时，会直接返回空对象 */
    express_extract:
      | {
          house_info: string
          poi_info: string
          town: string
          city: string
          district: string
          /** 电话号码 */
          tel: string
          /** 地址信息 */
          addr_info: string
          /** 姓名 */
          per: string
          prov: string
        }
      | {}
    /** 状态，'OK'，当传入的地址无效时，依然返回 OK 😅 */
    status: string
    /** 算法处理耗时 */
    time_used: {
      rt: {
        basic_chunking: number
        segment: number
        address_correct: number
        complete: number
        express_extract: number
        address_search: number
        structure: number
      }
      start: number
    }
  }
}

/**
 * Kuaidi100 API
 */
export declare namespace Kuaidi100 {
  /**
   * 地址解析
   */
  export interface AddressResolution {
    code: number
    data: {
      taskId: string
      result: {
        /** 用户输入的地址 */
        content: string
        mobile: number[]
        /** 姓名 */
        name: string
        /** 解析后的地址 */
        address: string
        xzq: {
          /** 完整地址：广东省,深圳市,南山区 */
          fullName: string
          /** 本字段将返回省份简称，例如：广东 */
          province: string
          /** 本字段返回市级行政区简称，例如：深圳市 */
          city: string
          /** 本字段返回区县简称，例如：南山区 */
          district: string
          /** 详细地址 */
          subArea: string
          /** 行政区父节点编码 */
          parentCode: string
          /** 行政区编码 */
          code: string
          /** 行政区级别 */
          level: 3
        }
      }[]
    }
    /** 是否成功，`success` */
    message: string
    /** 通常为 0，时间，可忽略 */
    time: number
    /** true提交成功，false失败 */
    success: boolean
  }

  /**
   * /fans-history API 中粉丝数项目
   */
  export interface FansItem {
    fans: number
    created_at: string
    date: string
  }
}
