// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               unknown
// source: index.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "";

/**
 * 礼物事件 V2
 * @since 发现于 Apr 24, 2025，可能出现的更早，只有某些特定的直播间内会出现
 * @example 测试直播间 https://live.bilibili.com/1883358196
 */
export interface SendGiftV2 {
  uid: number;
  uname: string;
  face: string;
  name_color?: string | undefined;
  guard_level?: number | undefined;
  svga_block?: number | undefined;
  send_master?: SendGiftV2_SendMaster | undefined;
  medal_info?: SendGiftV2_Medal | undefined;
  blind_gift?:
    | SendGiftV2_BlindGift
    | undefined;
  /** Guessed based on `SEND_GIFT` */
  gift_item: SendGiftV2_GiftItem[];
  switch?: boolean | undefined;
  test?: number | undefined;
  wealth?: SendGiftV2_Wealth | undefined;
  group_medal?: SendGiftV2_GroupMedal | undefined;
  sender_uinfo?: SendGiftV2_UInfo | undefined;
}

/** Baisc user info */
export interface SendGiftV2_UInfo {
  uid: number;
  base: SendGiftV2_UInfo_Base | undefined;
  medal?: SendGiftV2_UInfo_Medal | undefined;
  wealth?: SendGiftV2_Wealth | undefined;
}

export interface SendGiftV2_UInfo_Base {
  name: string;
  face: string;
  name_color?: number | undefined;
  is_mystery?: boolean | undefined;
}

export interface SendGiftV2_UInfo_Medal {
  name: string;
  level: number;
  color_start: number;
  color_end: number;
  color_border: number;
  color: number;
  id: number;
  typ: number;
  is_light: number;
  ruid: number;
  guard_level: number;
  score: number;
  guard_icon?: string | undefined;
}

/** Basic wealth info */
export interface SendGiftV2_Wealth {
  level: number;
}

export interface SendGiftV2_SendMaster {
  uid: number;
  uname: string;
}

export interface SendGiftV2_Medal {
  /** 对应主播 UID */
  ruid: number;
  /** 勋章等级 */
  level: number;
  /** 勋章名称 */
  name: string;
  is_lighted: number;
  guard_level: number;
}

export interface SendGiftV2_BlindGift {
  blind_gift_config_id: number;
  /**
   * 盲盒 id，非礼物 id
   * @example 32649
   */
  original_gift_id: number;
  /**
   * 盲盒名称
   * @example 星月盲盒
   */
  original_gift_name: string;
  from?:
    | number
    | undefined;
  /**
   * 盲盒动作
   * @example 爆出
   */
  gift_action: string;
  /**
   * 盲盒价格，非礼物爆出的虚拟价格
   * @example 5000
   */
  original_gift_price: number;
  /** 盲盒爆出的虚拟价格，但是 protobuf 中似乎不存在，需要从下方的 GiftItem 中判断 */
  gift_tip_price?: number | undefined;
}

export interface SendGiftV2_GiftItem {
  gift_id: number;
  gift_name: string;
  num: number;
  demarcation: number;
  /**
   * 礼物价格，单位金瓜子，5200 代表 5.2 元
   * 当此处为盲盒礼物时，此处显示的是虚拟价格（经验值）
   * @example 5200
   */
  price: number;
  /**
   * 通常等于 `price`
   * @example 5200
   */
  discount_price: number;
  /**
   * 硬币实际价格
   * 当此处为盲盒礼物时，此处显示的是实际支付价格
   * @example 5000
   */
  total_coin: number;
  /** @example `gold` */
  coin_type: string;
  /** @example `4658866727424159744` */
  tid: string;
  timestamp: number;
  super_batch_gift_num: number;
  /** @example `batch:gift:combo_id:2763:3546687211572101:31036:1751704815.1741` */
  batch_combo_id: string;
  combo_resources_id: number;
  /** 当此处为盲盒礼物时，此处显示的是虚拟价格（经验值） */
  combo_total_coin: number;
  combo_stay_time: number;
  magnification: number;
  show_batch_combo_send?:
    | boolean
    | undefined;
  /** @example `投喂` */
  action: string;
  effect_block?: number | undefined;
  is_special_batch?: number | undefined;
  float_sc_resource_id?: number | undefined;
  tag_image?: string | undefined;
  crit_prob?: number | undefined;
  rcost: number;
  test?: number | undefined;
  face_effect_type?: number | undefined;
  face_effect_id?: number | undefined;
  is_naming?: boolean | undefined;
  receive_user_info: SendGiftV2_GiftItem_ReceiveUserInfo | undefined;
  is_join_receiver?: boolean | undefined;
  bag_gift?: SendGiftV2_GiftItem_BagGiftInfo | undefined;
  gift_tag: number[];
  receiver_uinfo: SendGiftV2_UInfo | undefined;
  face_effect_v2?: SendGiftV2_GiftItem_FaceEffectV2 | undefined;
  gift_info:
    | SendGiftV2_GiftItem_GiftMaterialSnapShot
    | undefined;
  /**
   * 盲盒爆出的虚拟价格
   * @example 5200
   */
  gift_tip_price?: number | undefined;
}

export interface SendGiftV2_GiftItem_ReceiveUserInfo {
  uname: string;
  uid: number;
}

export interface SendGiftV2_GiftItem_BagGiftInfo {
  show_price: number;
  price_for_show: number;
}

export interface SendGiftV2_GiftItem_FaceEffectV2 {
  id: number;
  type: number;
}

export interface SendGiftV2_GiftItem_GiftMaterialSnapShot {
  img_basic: string;
  webp: string;
  effect_id: number;
  has_imaged_gift: number;
  gif?: string | undefined;
}

export interface SendGiftV2_GroupMedal {
  medal_id: number;
  name: string;
  is_lighted: number;
}

/**
 * 互动事件
 * @since Jul 4, 2025
 */
export interface InteractWordV2 {
  uid: number;
  username: string;
  msg_type: number;
  room_id: number;
  timestamp: number;
  timestamp_ms: number;
  /** 粉丝牌 */
  medal?: InteractWordV2_Medal | undefined;
  trigger_time: number;
  guard_type: number;
  f17: number;
  /** UInfo */
  uinfo: InteractWordV2_UInfo | undefined;
  relation_tail?: InteractWordV2_RelationTail | undefined;
}

export interface InteractWordV2_Medal {
  ruid: number;
  level: number;
  name: string;
  f4: number;
  f5: number;
  f6: number;
  f7: number;
  is_lighted: number;
  guard_level: number;
  room_id: number;
  f13: number;
}

export interface InteractWordV2_UInfo {
  uid: number;
  base: InteractWordV2_UInfo_Base | undefined;
  medal?:
    | InteractWordV2_UInfo_Medal
    | undefined;
  /** 荣耀等级 */
  wealth: InteractWordV2_UInfo_Wealth | undefined;
  f6: InteractWordV2_UInfo_Message6 | undefined;
}

export interface InteractWordV2_UInfo_Base {
  username: string;
  avatar: string;
  f3: string;
}

export interface InteractWordV2_UInfo_Medal {
  /** @example 奶糖花 */
  name: string;
  /** level */
  level: number;
  color_start: number;
  color_end: number;
  color_border: number;
  color: number;
  id: number;
  f9: number;
  ruid: number;
  f11: number;
  f12: number;
  guard_icon?:
    | string
    | undefined;
  /** @example #4775EFCC */
  v2_medal_color_start: number;
  /** @example #4775EFCC */
  v2_medal_color_end: number;
  /** @example #58A1F8FF */
  v2_medal_color_border: number;
  /** @example #FFFFFFFF */
  v2_medal_text: number;
  /** @example #000B7099 */
  v2_medal_level: number;
}

export interface InteractWordV2_UInfo_Wealth {
  level: number;
  /** @example ChronosWealth_5.png */
  icon?: string | undefined;
}

export interface InteractWordV2_UInfo_Message6 {
  f1: number;
  /** @example 2025-07-27 23:59:59 */
  f2: string;
}

export interface InteractWordV2_RelationTail {
  /** @example https://i0.hdslb.com/bfs/live/b9de5d510125c6f14cd68391d5a4878fe16356b3.png */
  tail_icon: string;
  /** @example TA常看你的直播，但还没有关注 */
  tail_guide_text: string;
  /** @example 1 */
  tail_type: number;
}

/**
 * 高能用户列表 v3
 * @since Jul 5, 2025
 */
export interface OnlineRankV3 {
  /** @example online_rank */
  rank_type: string;
  online_list: OnlineRankV3_OnlineList[];
}

export interface OnlineRankV3_OnlineList {
  uid: number;
  face: string;
  /** 贡献值，打赏电池数，string 格式😅 */
  score: string;
  uname: string;
  /** 当前排名 */
  rank: number;
  guard_level?: number | undefined;
  is_mystery?:
    | boolean
    | undefined;
  /** UInfo */
  uinfo?: OnlineRankV3_OnlineList_UInfo | undefined;
}

export interface OnlineRankV3_OnlineList_UInfo {
  uid: number;
  base: OnlineRankV3_OnlineList_UInfo_Base | undefined;
  guard?: OnlineRankV3_OnlineList_UInfo_Guard | undefined;
}

export interface OnlineRankV3_OnlineList_UInfo_Base {
  name: string;
  face: string;
  name_color: number;
  is_mystery: boolean;
}

export interface OnlineRankV3_OnlineList_UInfo_Guard {
  level: number;
  /** @example 2025-08-30 23:59:59 */
  expired_str: string;
}

function createBaseSendGiftV2(): SendGiftV2 {
  return {
    uid: 0,
    uname: "",
    face: "",
    name_color: undefined,
    guard_level: undefined,
    svga_block: undefined,
    send_master: undefined,
    medal_info: undefined,
    blind_gift: undefined,
    gift_item: [],
    switch: undefined,
    test: undefined,
    wealth: undefined,
    group_medal: undefined,
    sender_uinfo: undefined,
  };
}

export const SendGiftV2: MessageFns<SendGiftV2> = {
  encode(message: SendGiftV2, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.uid !== 0) {
      writer.uint32(8).uint64(message.uid);
    }
    if (message.uname !== "") {
      writer.uint32(18).string(message.uname);
    }
    if (message.face !== "") {
      writer.uint32(26).string(message.face);
    }
    if (message.name_color !== undefined) {
      writer.uint32(34).string(message.name_color);
    }
    if (message.guard_level !== undefined) {
      writer.uint32(40).int64(message.guard_level);
    }
    if (message.svga_block !== undefined) {
      writer.uint32(48).int64(message.svga_block);
    }
    if (message.send_master !== undefined) {
      SendGiftV2_SendMaster.encode(message.send_master, writer.uint32(58).fork()).join();
    }
    if (message.medal_info !== undefined) {
      SendGiftV2_Medal.encode(message.medal_info, writer.uint32(66).fork()).join();
    }
    if (message.blind_gift !== undefined) {
      SendGiftV2_BlindGift.encode(message.blind_gift, writer.uint32(74).fork()).join();
    }
    for (const v of message.gift_item) {
      SendGiftV2_GiftItem.encode(v!, writer.uint32(82).fork()).join();
    }
    if (message.switch !== undefined) {
      writer.uint32(88).bool(message.switch);
    }
    if (message.test !== undefined) {
      writer.uint32(96).int64(message.test);
    }
    if (message.wealth !== undefined) {
      SendGiftV2_Wealth.encode(message.wealth, writer.uint32(106).fork()).join();
    }
    if (message.group_medal !== undefined) {
      SendGiftV2_GroupMedal.encode(message.group_medal, writer.uint32(114).fork()).join();
    }
    if (message.sender_uinfo !== undefined) {
      SendGiftV2_UInfo.encode(message.sender_uinfo, writer.uint32(122).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendGiftV2 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendGiftV2();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.uid = longToNumber(reader.uint64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.uname = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.face = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.name_color = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.guard_level = longToNumber(reader.int64());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.svga_block = longToNumber(reader.int64());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.send_master = SendGiftV2_SendMaster.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.medal_info = SendGiftV2_Medal.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.blind_gift = SendGiftV2_BlindGift.decode(reader, reader.uint32());
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.gift_item.push(SendGiftV2_GiftItem.decode(reader, reader.uint32()));
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.switch = reader.bool();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.test = longToNumber(reader.int64());
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.wealth = SendGiftV2_Wealth.decode(reader, reader.uint32());
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.group_medal = SendGiftV2_GroupMedal.decode(reader, reader.uint32());
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.sender_uinfo = SendGiftV2_UInfo.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SendGiftV2 {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      uname: isSet(object.uname) ? globalThis.String(object.uname) : "",
      face: isSet(object.face) ? globalThis.String(object.face) : "",
      name_color: isSet(object.name_color) ? globalThis.String(object.name_color) : undefined,
      guard_level: isSet(object.guard_level) ? globalThis.Number(object.guard_level) : undefined,
      svga_block: isSet(object.svga_block) ? globalThis.Number(object.svga_block) : undefined,
      send_master: isSet(object.send_master) ? SendGiftV2_SendMaster.fromJSON(object.send_master) : undefined,
      medal_info: isSet(object.medal_info) ? SendGiftV2_Medal.fromJSON(object.medal_info) : undefined,
      blind_gift: isSet(object.blind_gift) ? SendGiftV2_BlindGift.fromJSON(object.blind_gift) : undefined,
      gift_item: globalThis.Array.isArray(object?.gift_item)
        ? object.gift_item.map((e: any) => SendGiftV2_GiftItem.fromJSON(e))
        : [],
      switch: isSet(object.switch) ? globalThis.Boolean(object.switch) : undefined,
      test: isSet(object.test) ? globalThis.Number(object.test) : undefined,
      wealth: isSet(object.wealth) ? SendGiftV2_Wealth.fromJSON(object.wealth) : undefined,
      group_medal: isSet(object.group_medal) ? SendGiftV2_GroupMedal.fromJSON(object.group_medal) : undefined,
      sender_uinfo: isSet(object.sender_uinfo) ? SendGiftV2_UInfo.fromJSON(object.sender_uinfo) : undefined,
    };
  },

  toJSON(message: SendGiftV2): unknown {
    const obj: any = {};
    if (message.uid !== 0) {
      obj.uid = Math.round(message.uid);
    }
    if (message.uname !== "") {
      obj.uname = message.uname;
    }
    if (message.face !== "") {
      obj.face = message.face;
    }
    if (message.name_color !== undefined) {
      obj.name_color = message.name_color;
    }
    if (message.guard_level !== undefined) {
      obj.guard_level = Math.round(message.guard_level);
    }
    if (message.svga_block !== undefined) {
      obj.svga_block = Math.round(message.svga_block);
    }
    if (message.send_master !== undefined) {
      obj.send_master = SendGiftV2_SendMaster.toJSON(message.send_master);
    }
    if (message.medal_info !== undefined) {
      obj.medal_info = SendGiftV2_Medal.toJSON(message.medal_info);
    }
    if (message.blind_gift !== undefined) {
      obj.blind_gift = SendGiftV2_BlindGift.toJSON(message.blind_gift);
    }
    if (message.gift_item?.length) {
      obj.gift_item = message.gift_item.map((e) => SendGiftV2_GiftItem.toJSON(e));
    }
    if (message.switch !== undefined) {
      obj.switch = message.switch;
    }
    if (message.test !== undefined) {
      obj.test = Math.round(message.test);
    }
    if (message.wealth !== undefined) {
      obj.wealth = SendGiftV2_Wealth.toJSON(message.wealth);
    }
    if (message.group_medal !== undefined) {
      obj.group_medal = SendGiftV2_GroupMedal.toJSON(message.group_medal);
    }
    if (message.sender_uinfo !== undefined) {
      obj.sender_uinfo = SendGiftV2_UInfo.toJSON(message.sender_uinfo);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SendGiftV2>, I>>(base?: I): SendGiftV2 {
    return SendGiftV2.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGiftV2>, I>>(object: I): SendGiftV2 {
    const message = createBaseSendGiftV2();
    message.uid = object.uid ?? 0;
    message.uname = object.uname ?? "";
    message.face = object.face ?? "";
    message.name_color = object.name_color ?? undefined;
    message.guard_level = object.guard_level ?? undefined;
    message.svga_block = object.svga_block ?? undefined;
    message.send_master = (object.send_master !== undefined && object.send_master !== null)
      ? SendGiftV2_SendMaster.fromPartial(object.send_master)
      : undefined;
    message.medal_info = (object.medal_info !== undefined && object.medal_info !== null)
      ? SendGiftV2_Medal.fromPartial(object.medal_info)
      : undefined;
    message.blind_gift = (object.blind_gift !== undefined && object.blind_gift !== null)
      ? SendGiftV2_BlindGift.fromPartial(object.blind_gift)
      : undefined;
    message.gift_item = object.gift_item?.map((e) => SendGiftV2_GiftItem.fromPartial(e)) || [];
    message.switch = object.switch ?? undefined;
    message.test = object.test ?? undefined;
    message.wealth = (object.wealth !== undefined && object.wealth !== null)
      ? SendGiftV2_Wealth.fromPartial(object.wealth)
      : undefined;
    message.group_medal = (object.group_medal !== undefined && object.group_medal !== null)
      ? SendGiftV2_GroupMedal.fromPartial(object.group_medal)
      : undefined;
    message.sender_uinfo = (object.sender_uinfo !== undefined && object.sender_uinfo !== null)
      ? SendGiftV2_UInfo.fromPartial(object.sender_uinfo)
      : undefined;
    return message;
  },
};

function createBaseSendGiftV2_UInfo(): SendGiftV2_UInfo {
  return { uid: 0, base: undefined, medal: undefined, wealth: undefined };
}

export const SendGiftV2_UInfo: MessageFns<SendGiftV2_UInfo> = {
  encode(message: SendGiftV2_UInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.uid !== 0) {
      writer.uint32(8).uint64(message.uid);
    }
    if (message.base !== undefined) {
      SendGiftV2_UInfo_Base.encode(message.base, writer.uint32(18).fork()).join();
    }
    if (message.medal !== undefined) {
      SendGiftV2_UInfo_Medal.encode(message.medal, writer.uint32(26).fork()).join();
    }
    if (message.wealth !== undefined) {
      SendGiftV2_Wealth.encode(message.wealth, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendGiftV2_UInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendGiftV2_UInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.uid = longToNumber(reader.uint64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.base = SendGiftV2_UInfo_Base.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.medal = SendGiftV2_UInfo_Medal.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.wealth = SendGiftV2_Wealth.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SendGiftV2_UInfo {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      base: isSet(object.base) ? SendGiftV2_UInfo_Base.fromJSON(object.base) : undefined,
      medal: isSet(object.medal) ? SendGiftV2_UInfo_Medal.fromJSON(object.medal) : undefined,
      wealth: isSet(object.wealth) ? SendGiftV2_Wealth.fromJSON(object.wealth) : undefined,
    };
  },

  toJSON(message: SendGiftV2_UInfo): unknown {
    const obj: any = {};
    if (message.uid !== 0) {
      obj.uid = Math.round(message.uid);
    }
    if (message.base !== undefined) {
      obj.base = SendGiftV2_UInfo_Base.toJSON(message.base);
    }
    if (message.medal !== undefined) {
      obj.medal = SendGiftV2_UInfo_Medal.toJSON(message.medal);
    }
    if (message.wealth !== undefined) {
      obj.wealth = SendGiftV2_Wealth.toJSON(message.wealth);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SendGiftV2_UInfo>, I>>(base?: I): SendGiftV2_UInfo {
    return SendGiftV2_UInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGiftV2_UInfo>, I>>(object: I): SendGiftV2_UInfo {
    const message = createBaseSendGiftV2_UInfo();
    message.uid = object.uid ?? 0;
    message.base = (object.base !== undefined && object.base !== null)
      ? SendGiftV2_UInfo_Base.fromPartial(object.base)
      : undefined;
    message.medal = (object.medal !== undefined && object.medal !== null)
      ? SendGiftV2_UInfo_Medal.fromPartial(object.medal)
      : undefined;
    message.wealth = (object.wealth !== undefined && object.wealth !== null)
      ? SendGiftV2_Wealth.fromPartial(object.wealth)
      : undefined;
    return message;
  },
};

function createBaseSendGiftV2_UInfo_Base(): SendGiftV2_UInfo_Base {
  return { name: "", face: "", name_color: undefined, is_mystery: undefined };
}

export const SendGiftV2_UInfo_Base: MessageFns<SendGiftV2_UInfo_Base> = {
  encode(message: SendGiftV2_UInfo_Base, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.face !== "") {
      writer.uint32(18).string(message.face);
    }
    if (message.name_color !== undefined) {
      writer.uint32(24).int32(message.name_color);
    }
    if (message.is_mystery !== undefined) {
      writer.uint32(32).bool(message.is_mystery);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendGiftV2_UInfo_Base {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendGiftV2_UInfo_Base();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.face = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.name_color = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.is_mystery = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SendGiftV2_UInfo_Base {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      face: isSet(object.face) ? globalThis.String(object.face) : "",
      name_color: isSet(object.name_color) ? globalThis.Number(object.name_color) : undefined,
      is_mystery: isSet(object.is_mystery) ? globalThis.Boolean(object.is_mystery) : undefined,
    };
  },

  toJSON(message: SendGiftV2_UInfo_Base): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.face !== "") {
      obj.face = message.face;
    }
    if (message.name_color !== undefined) {
      obj.name_color = Math.round(message.name_color);
    }
    if (message.is_mystery !== undefined) {
      obj.is_mystery = message.is_mystery;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SendGiftV2_UInfo_Base>, I>>(base?: I): SendGiftV2_UInfo_Base {
    return SendGiftV2_UInfo_Base.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGiftV2_UInfo_Base>, I>>(object: I): SendGiftV2_UInfo_Base {
    const message = createBaseSendGiftV2_UInfo_Base();
    message.name = object.name ?? "";
    message.face = object.face ?? "";
    message.name_color = object.name_color ?? undefined;
    message.is_mystery = object.is_mystery ?? undefined;
    return message;
  },
};

function createBaseSendGiftV2_UInfo_Medal(): SendGiftV2_UInfo_Medal {
  return {
    name: "",
    level: 0,
    color_start: 0,
    color_end: 0,
    color_border: 0,
    color: 0,
    id: 0,
    typ: 0,
    is_light: 0,
    ruid: 0,
    guard_level: 0,
    score: 0,
    guard_icon: undefined,
  };
}

export const SendGiftV2_UInfo_Medal: MessageFns<SendGiftV2_UInfo_Medal> = {
  encode(message: SendGiftV2_UInfo_Medal, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.level !== 0) {
      writer.uint32(16).int64(message.level);
    }
    if (message.color_start !== 0) {
      writer.uint32(24).int64(message.color_start);
    }
    if (message.color_end !== 0) {
      writer.uint32(32).int64(message.color_end);
    }
    if (message.color_border !== 0) {
      writer.uint32(40).int64(message.color_border);
    }
    if (message.color !== 0) {
      writer.uint32(48).int64(message.color);
    }
    if (message.id !== 0) {
      writer.uint32(56).int64(message.id);
    }
    if (message.typ !== 0) {
      writer.uint32(64).int32(message.typ);
    }
    if (message.is_light !== 0) {
      writer.uint32(72).int64(message.is_light);
    }
    if (message.ruid !== 0) {
      writer.uint32(80).int64(message.ruid);
    }
    if (message.guard_level !== 0) {
      writer.uint32(88).int64(message.guard_level);
    }
    if (message.score !== 0) {
      writer.uint32(96).int64(message.score);
    }
    if (message.guard_icon !== undefined) {
      writer.uint32(106).string(message.guard_icon);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendGiftV2_UInfo_Medal {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendGiftV2_UInfo_Medal();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.level = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.color_start = longToNumber(reader.int64());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.color_end = longToNumber(reader.int64());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.color_border = longToNumber(reader.int64());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.color = longToNumber(reader.int64());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.typ = reader.int32();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.is_light = longToNumber(reader.int64());
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.ruid = longToNumber(reader.int64());
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.guard_level = longToNumber(reader.int64());
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.score = longToNumber(reader.int64());
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.guard_icon = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SendGiftV2_UInfo_Medal {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      level: isSet(object.level) ? globalThis.Number(object.level) : 0,
      color_start: isSet(object.color_start) ? globalThis.Number(object.color_start) : 0,
      color_end: isSet(object.color_end) ? globalThis.Number(object.color_end) : 0,
      color_border: isSet(object.color_border) ? globalThis.Number(object.color_border) : 0,
      color: isSet(object.color) ? globalThis.Number(object.color) : 0,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      typ: isSet(object.typ) ? globalThis.Number(object.typ) : 0,
      is_light: isSet(object.is_light) ? globalThis.Number(object.is_light) : 0,
      ruid: isSet(object.ruid) ? globalThis.Number(object.ruid) : 0,
      guard_level: isSet(object.guard_level) ? globalThis.Number(object.guard_level) : 0,
      score: isSet(object.score) ? globalThis.Number(object.score) : 0,
      guard_icon: isSet(object.guard_icon) ? globalThis.String(object.guard_icon) : undefined,
    };
  },

  toJSON(message: SendGiftV2_UInfo_Medal): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.level !== 0) {
      obj.level = Math.round(message.level);
    }
    if (message.color_start !== 0) {
      obj.color_start = Math.round(message.color_start);
    }
    if (message.color_end !== 0) {
      obj.color_end = Math.round(message.color_end);
    }
    if (message.color_border !== 0) {
      obj.color_border = Math.round(message.color_border);
    }
    if (message.color !== 0) {
      obj.color = Math.round(message.color);
    }
    if (message.id !== 0) {
      obj.id = Math.round(message.id);
    }
    if (message.typ !== 0) {
      obj.typ = Math.round(message.typ);
    }
    if (message.is_light !== 0) {
      obj.is_light = Math.round(message.is_light);
    }
    if (message.ruid !== 0) {
      obj.ruid = Math.round(message.ruid);
    }
    if (message.guard_level !== 0) {
      obj.guard_level = Math.round(message.guard_level);
    }
    if (message.score !== 0) {
      obj.score = Math.round(message.score);
    }
    if (message.guard_icon !== undefined) {
      obj.guard_icon = message.guard_icon;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SendGiftV2_UInfo_Medal>, I>>(base?: I): SendGiftV2_UInfo_Medal {
    return SendGiftV2_UInfo_Medal.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGiftV2_UInfo_Medal>, I>>(object: I): SendGiftV2_UInfo_Medal {
    const message = createBaseSendGiftV2_UInfo_Medal();
    message.name = object.name ?? "";
    message.level = object.level ?? 0;
    message.color_start = object.color_start ?? 0;
    message.color_end = object.color_end ?? 0;
    message.color_border = object.color_border ?? 0;
    message.color = object.color ?? 0;
    message.id = object.id ?? 0;
    message.typ = object.typ ?? 0;
    message.is_light = object.is_light ?? 0;
    message.ruid = object.ruid ?? 0;
    message.guard_level = object.guard_level ?? 0;
    message.score = object.score ?? 0;
    message.guard_icon = object.guard_icon ?? undefined;
    return message;
  },
};

function createBaseSendGiftV2_Wealth(): SendGiftV2_Wealth {
  return { level: 0 };
}

export const SendGiftV2_Wealth: MessageFns<SendGiftV2_Wealth> = {
  encode(message: SendGiftV2_Wealth, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.level !== 0) {
      writer.uint32(8).int64(message.level);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendGiftV2_Wealth {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendGiftV2_Wealth();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.level = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SendGiftV2_Wealth {
    return { level: isSet(object.level) ? globalThis.Number(object.level) : 0 };
  },

  toJSON(message: SendGiftV2_Wealth): unknown {
    const obj: any = {};
    if (message.level !== 0) {
      obj.level = Math.round(message.level);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SendGiftV2_Wealth>, I>>(base?: I): SendGiftV2_Wealth {
    return SendGiftV2_Wealth.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGiftV2_Wealth>, I>>(object: I): SendGiftV2_Wealth {
    const message = createBaseSendGiftV2_Wealth();
    message.level = object.level ?? 0;
    return message;
  },
};

function createBaseSendGiftV2_SendMaster(): SendGiftV2_SendMaster {
  return { uid: 0, uname: "" };
}

export const SendGiftV2_SendMaster: MessageFns<SendGiftV2_SendMaster> = {
  encode(message: SendGiftV2_SendMaster, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.uid !== 0) {
      writer.uint32(8).uint64(message.uid);
    }
    if (message.uname !== "") {
      writer.uint32(18).string(message.uname);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendGiftV2_SendMaster {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendGiftV2_SendMaster();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.uid = longToNumber(reader.uint64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.uname = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SendGiftV2_SendMaster {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      uname: isSet(object.uname) ? globalThis.String(object.uname) : "",
    };
  },

  toJSON(message: SendGiftV2_SendMaster): unknown {
    const obj: any = {};
    if (message.uid !== 0) {
      obj.uid = Math.round(message.uid);
    }
    if (message.uname !== "") {
      obj.uname = message.uname;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SendGiftV2_SendMaster>, I>>(base?: I): SendGiftV2_SendMaster {
    return SendGiftV2_SendMaster.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGiftV2_SendMaster>, I>>(object: I): SendGiftV2_SendMaster {
    const message = createBaseSendGiftV2_SendMaster();
    message.uid = object.uid ?? 0;
    message.uname = object.uname ?? "";
    return message;
  },
};

function createBaseSendGiftV2_Medal(): SendGiftV2_Medal {
  return { ruid: 0, level: 0, name: "", is_lighted: 0, guard_level: 0 };
}

export const SendGiftV2_Medal: MessageFns<SendGiftV2_Medal> = {
  encode(message: SendGiftV2_Medal, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ruid !== 0) {
      writer.uint32(8).uint64(message.ruid);
    }
    if (message.level !== 0) {
      writer.uint32(40).uint32(message.level);
    }
    if (message.name !== "") {
      writer.uint32(50).string(message.name);
    }
    if (message.is_lighted !== 0) {
      writer.uint32(88).uint32(message.is_lighted);
    }
    if (message.guard_level !== 0) {
      writer.uint32(96).uint32(message.guard_level);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendGiftV2_Medal {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendGiftV2_Medal();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.ruid = longToNumber(reader.uint64());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.level = reader.uint32();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.is_lighted = reader.uint32();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.guard_level = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SendGiftV2_Medal {
    return {
      ruid: isSet(object.ruid) ? globalThis.Number(object.ruid) : 0,
      level: isSet(object.level) ? globalThis.Number(object.level) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      is_lighted: isSet(object.is_lighted) ? globalThis.Number(object.is_lighted) : 0,
      guard_level: isSet(object.guard_level) ? globalThis.Number(object.guard_level) : 0,
    };
  },

  toJSON(message: SendGiftV2_Medal): unknown {
    const obj: any = {};
    if (message.ruid !== 0) {
      obj.ruid = Math.round(message.ruid);
    }
    if (message.level !== 0) {
      obj.level = Math.round(message.level);
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.is_lighted !== 0) {
      obj.is_lighted = Math.round(message.is_lighted);
    }
    if (message.guard_level !== 0) {
      obj.guard_level = Math.round(message.guard_level);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SendGiftV2_Medal>, I>>(base?: I): SendGiftV2_Medal {
    return SendGiftV2_Medal.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGiftV2_Medal>, I>>(object: I): SendGiftV2_Medal {
    const message = createBaseSendGiftV2_Medal();
    message.ruid = object.ruid ?? 0;
    message.level = object.level ?? 0;
    message.name = object.name ?? "";
    message.is_lighted = object.is_lighted ?? 0;
    message.guard_level = object.guard_level ?? 0;
    return message;
  },
};

function createBaseSendGiftV2_BlindGift(): SendGiftV2_BlindGift {
  return {
    blind_gift_config_id: 0,
    original_gift_id: 0,
    original_gift_name: "",
    from: undefined,
    gift_action: "",
    original_gift_price: 0,
    gift_tip_price: undefined,
  };
}

export const SendGiftV2_BlindGift: MessageFns<SendGiftV2_BlindGift> = {
  encode(message: SendGiftV2_BlindGift, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.blind_gift_config_id !== 0) {
      writer.uint32(8).int64(message.blind_gift_config_id);
    }
    if (message.original_gift_id !== 0) {
      writer.uint32(16).int64(message.original_gift_id);
    }
    if (message.original_gift_name !== "") {
      writer.uint32(26).string(message.original_gift_name);
    }
    if (message.from !== undefined) {
      writer.uint32(32).int64(message.from);
    }
    if (message.gift_action !== "") {
      writer.uint32(42).string(message.gift_action);
    }
    if (message.original_gift_price !== 0) {
      writer.uint32(48).int64(message.original_gift_price);
    }
    if (message.gift_tip_price !== undefined) {
      writer.uint32(56).int64(message.gift_tip_price);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendGiftV2_BlindGift {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendGiftV2_BlindGift();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.blind_gift_config_id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.original_gift_id = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.original_gift_name = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.from = longToNumber(reader.int64());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.gift_action = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.original_gift_price = longToNumber(reader.int64());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.gift_tip_price = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SendGiftV2_BlindGift {
    return {
      blind_gift_config_id: isSet(object.blind_gift_config_id) ? globalThis.Number(object.blind_gift_config_id) : 0,
      original_gift_id: isSet(object.original_gift_id) ? globalThis.Number(object.original_gift_id) : 0,
      original_gift_name: isSet(object.original_gift_name) ? globalThis.String(object.original_gift_name) : "",
      from: isSet(object.from) ? globalThis.Number(object.from) : undefined,
      gift_action: isSet(object.gift_action) ? globalThis.String(object.gift_action) : "",
      original_gift_price: isSet(object.original_gift_price) ? globalThis.Number(object.original_gift_price) : 0,
      gift_tip_price: isSet(object.gift_tip_price) ? globalThis.Number(object.gift_tip_price) : undefined,
    };
  },

  toJSON(message: SendGiftV2_BlindGift): unknown {
    const obj: any = {};
    if (message.blind_gift_config_id !== 0) {
      obj.blind_gift_config_id = Math.round(message.blind_gift_config_id);
    }
    if (message.original_gift_id !== 0) {
      obj.original_gift_id = Math.round(message.original_gift_id);
    }
    if (message.original_gift_name !== "") {
      obj.original_gift_name = message.original_gift_name;
    }
    if (message.from !== undefined) {
      obj.from = Math.round(message.from);
    }
    if (message.gift_action !== "") {
      obj.gift_action = message.gift_action;
    }
    if (message.original_gift_price !== 0) {
      obj.original_gift_price = Math.round(message.original_gift_price);
    }
    if (message.gift_tip_price !== undefined) {
      obj.gift_tip_price = Math.round(message.gift_tip_price);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SendGiftV2_BlindGift>, I>>(base?: I): SendGiftV2_BlindGift {
    return SendGiftV2_BlindGift.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGiftV2_BlindGift>, I>>(object: I): SendGiftV2_BlindGift {
    const message = createBaseSendGiftV2_BlindGift();
    message.blind_gift_config_id = object.blind_gift_config_id ?? 0;
    message.original_gift_id = object.original_gift_id ?? 0;
    message.original_gift_name = object.original_gift_name ?? "";
    message.from = object.from ?? undefined;
    message.gift_action = object.gift_action ?? "";
    message.original_gift_price = object.original_gift_price ?? 0;
    message.gift_tip_price = object.gift_tip_price ?? undefined;
    return message;
  },
};

function createBaseSendGiftV2_GiftItem(): SendGiftV2_GiftItem {
  return {
    gift_id: 0,
    gift_name: "",
    num: 0,
    demarcation: 0,
    price: 0,
    discount_price: 0,
    total_coin: 0,
    coin_type: "",
    tid: "",
    timestamp: 0,
    super_batch_gift_num: 0,
    batch_combo_id: "",
    combo_resources_id: 0,
    combo_total_coin: 0,
    combo_stay_time: 0,
    magnification: 0,
    show_batch_combo_send: undefined,
    action: "",
    effect_block: undefined,
    is_special_batch: undefined,
    float_sc_resource_id: undefined,
    tag_image: undefined,
    crit_prob: undefined,
    rcost: 0,
    test: undefined,
    face_effect_type: undefined,
    face_effect_id: undefined,
    is_naming: undefined,
    receive_user_info: undefined,
    is_join_receiver: undefined,
    bag_gift: undefined,
    gift_tag: [],
    receiver_uinfo: undefined,
    face_effect_v2: undefined,
    gift_info: undefined,
    gift_tip_price: undefined,
  };
}

export const SendGiftV2_GiftItem: MessageFns<SendGiftV2_GiftItem> = {
  encode(message: SendGiftV2_GiftItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.gift_id !== 0) {
      writer.uint32(8).uint32(message.gift_id);
    }
    if (message.gift_name !== "") {
      writer.uint32(18).string(message.gift_name);
    }
    if (message.num !== 0) {
      writer.uint32(24).uint32(message.num);
    }
    if (message.demarcation !== 0) {
      writer.uint32(32).uint64(message.demarcation);
    }
    if (message.price !== 0) {
      writer.uint32(40).uint64(message.price);
    }
    if (message.discount_price !== 0) {
      writer.uint32(48).uint64(message.discount_price);
    }
    if (message.total_coin !== 0) {
      writer.uint32(56).uint64(message.total_coin);
    }
    if (message.coin_type !== "") {
      writer.uint32(66).string(message.coin_type);
    }
    if (message.tid !== "") {
      writer.uint32(74).string(message.tid);
    }
    if (message.timestamp !== 0) {
      writer.uint32(80).uint64(message.timestamp);
    }
    if (message.super_batch_gift_num !== 0) {
      writer.uint32(88).uint64(message.super_batch_gift_num);
    }
    if (message.batch_combo_id !== "") {
      writer.uint32(98).string(message.batch_combo_id);
    }
    if (message.combo_resources_id !== 0) {
      writer.uint32(104).uint64(message.combo_resources_id);
    }
    if (message.combo_total_coin !== 0) {
      writer.uint32(112).uint64(message.combo_total_coin);
    }
    if (message.combo_stay_time !== 0) {
      writer.uint32(120).uint64(message.combo_stay_time);
    }
    if (message.magnification !== 0) {
      writer.uint32(133).float(message.magnification);
    }
    if (message.show_batch_combo_send !== undefined) {
      writer.uint32(136).bool(message.show_batch_combo_send);
    }
    if (message.action !== "") {
      writer.uint32(146).string(message.action);
    }
    if (message.effect_block !== undefined) {
      writer.uint32(152).int64(message.effect_block);
    }
    if (message.is_special_batch !== undefined) {
      writer.uint32(160).int64(message.is_special_batch);
    }
    if (message.float_sc_resource_id !== undefined) {
      writer.uint32(168).int64(message.float_sc_resource_id);
    }
    if (message.tag_image !== undefined) {
      writer.uint32(178).string(message.tag_image);
    }
    if (message.crit_prob !== undefined) {
      writer.uint32(184).int64(message.crit_prob);
    }
    if (message.rcost !== 0) {
      writer.uint32(192).uint64(message.rcost);
    }
    if (message.test !== undefined) {
      writer.uint32(200).int64(message.test);
    }
    if (message.face_effect_type !== undefined) {
      writer.uint32(208).int64(message.face_effect_type);
    }
    if (message.face_effect_id !== undefined) {
      writer.uint32(216).int64(message.face_effect_id);
    }
    if (message.is_naming !== undefined) {
      writer.uint32(224).bool(message.is_naming);
    }
    if (message.receive_user_info !== undefined) {
      SendGiftV2_GiftItem_ReceiveUserInfo.encode(message.receive_user_info, writer.uint32(234).fork()).join();
    }
    if (message.is_join_receiver !== undefined) {
      writer.uint32(240).bool(message.is_join_receiver);
    }
    if (message.bag_gift !== undefined) {
      SendGiftV2_GiftItem_BagGiftInfo.encode(message.bag_gift, writer.uint32(250).fork()).join();
    }
    writer.uint32(258).fork();
    for (const v of message.gift_tag) {
      writer.int64(v);
    }
    writer.join();
    if (message.receiver_uinfo !== undefined) {
      SendGiftV2_UInfo.encode(message.receiver_uinfo, writer.uint32(266).fork()).join();
    }
    if (message.face_effect_v2 !== undefined) {
      SendGiftV2_GiftItem_FaceEffectV2.encode(message.face_effect_v2, writer.uint32(274).fork()).join();
    }
    if (message.gift_info !== undefined) {
      SendGiftV2_GiftItem_GiftMaterialSnapShot.encode(message.gift_info, writer.uint32(282).fork()).join();
    }
    if (message.gift_tip_price !== undefined) {
      writer.uint32(288).int64(message.gift_tip_price);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendGiftV2_GiftItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendGiftV2_GiftItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.gift_id = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.gift_name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.num = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.demarcation = longToNumber(reader.uint64());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.price = longToNumber(reader.uint64());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.discount_price = longToNumber(reader.uint64());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.total_coin = longToNumber(reader.uint64());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.coin_type = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.tid = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.timestamp = longToNumber(reader.uint64());
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.super_batch_gift_num = longToNumber(reader.uint64());
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.batch_combo_id = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.combo_resources_id = longToNumber(reader.uint64());
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.combo_total_coin = longToNumber(reader.uint64());
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.combo_stay_time = longToNumber(reader.uint64());
          continue;
        }
        case 16: {
          if (tag !== 133) {
            break;
          }

          message.magnification = reader.float();
          continue;
        }
        case 17: {
          if (tag !== 136) {
            break;
          }

          message.show_batch_combo_send = reader.bool();
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.action = reader.string();
          continue;
        }
        case 19: {
          if (tag !== 152) {
            break;
          }

          message.effect_block = longToNumber(reader.int64());
          continue;
        }
        case 20: {
          if (tag !== 160) {
            break;
          }

          message.is_special_batch = longToNumber(reader.int64());
          continue;
        }
        case 21: {
          if (tag !== 168) {
            break;
          }

          message.float_sc_resource_id = longToNumber(reader.int64());
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.tag_image = reader.string();
          continue;
        }
        case 23: {
          if (tag !== 184) {
            break;
          }

          message.crit_prob = longToNumber(reader.int64());
          continue;
        }
        case 24: {
          if (tag !== 192) {
            break;
          }

          message.rcost = longToNumber(reader.uint64());
          continue;
        }
        case 25: {
          if (tag !== 200) {
            break;
          }

          message.test = longToNumber(reader.int64());
          continue;
        }
        case 26: {
          if (tag !== 208) {
            break;
          }

          message.face_effect_type = longToNumber(reader.int64());
          continue;
        }
        case 27: {
          if (tag !== 216) {
            break;
          }

          message.face_effect_id = longToNumber(reader.int64());
          continue;
        }
        case 28: {
          if (tag !== 224) {
            break;
          }

          message.is_naming = reader.bool();
          continue;
        }
        case 29: {
          if (tag !== 234) {
            break;
          }

          message.receive_user_info = SendGiftV2_GiftItem_ReceiveUserInfo.decode(reader, reader.uint32());
          continue;
        }
        case 30: {
          if (tag !== 240) {
            break;
          }

          message.is_join_receiver = reader.bool();
          continue;
        }
        case 31: {
          if (tag !== 250) {
            break;
          }

          message.bag_gift = SendGiftV2_GiftItem_BagGiftInfo.decode(reader, reader.uint32());
          continue;
        }
        case 32: {
          if (tag === 256) {
            message.gift_tag.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 258) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.gift_tag.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 33: {
          if (tag !== 266) {
            break;
          }

          message.receiver_uinfo = SendGiftV2_UInfo.decode(reader, reader.uint32());
          continue;
        }
        case 34: {
          if (tag !== 274) {
            break;
          }

          message.face_effect_v2 = SendGiftV2_GiftItem_FaceEffectV2.decode(reader, reader.uint32());
          continue;
        }
        case 35: {
          if (tag !== 282) {
            break;
          }

          message.gift_info = SendGiftV2_GiftItem_GiftMaterialSnapShot.decode(reader, reader.uint32());
          continue;
        }
        case 36: {
          if (tag !== 288) {
            break;
          }

          message.gift_tip_price = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SendGiftV2_GiftItem {
    return {
      gift_id: isSet(object.gift_id) ? globalThis.Number(object.gift_id) : 0,
      gift_name: isSet(object.gift_name) ? globalThis.String(object.gift_name) : "",
      num: isSet(object.num) ? globalThis.Number(object.num) : 0,
      demarcation: isSet(object.demarcation) ? globalThis.Number(object.demarcation) : 0,
      price: isSet(object.price) ? globalThis.Number(object.price) : 0,
      discount_price: isSet(object.discount_price) ? globalThis.Number(object.discount_price) : 0,
      total_coin: isSet(object.total_coin) ? globalThis.Number(object.total_coin) : 0,
      coin_type: isSet(object.coin_type) ? globalThis.String(object.coin_type) : "",
      tid: isSet(object.tid) ? globalThis.String(object.tid) : "",
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      super_batch_gift_num: isSet(object.super_batch_gift_num) ? globalThis.Number(object.super_batch_gift_num) : 0,
      batch_combo_id: isSet(object.batch_combo_id) ? globalThis.String(object.batch_combo_id) : "",
      combo_resources_id: isSet(object.combo_resources_id) ? globalThis.Number(object.combo_resources_id) : 0,
      combo_total_coin: isSet(object.combo_total_coin) ? globalThis.Number(object.combo_total_coin) : 0,
      combo_stay_time: isSet(object.combo_stay_time) ? globalThis.Number(object.combo_stay_time) : 0,
      magnification: isSet(object.magnification) ? globalThis.Number(object.magnification) : 0,
      show_batch_combo_send: isSet(object.show_batch_combo_send)
        ? globalThis.Boolean(object.show_batch_combo_send)
        : undefined,
      action: isSet(object.action) ? globalThis.String(object.action) : "",
      effect_block: isSet(object.effect_block) ? globalThis.Number(object.effect_block) : undefined,
      is_special_batch: isSet(object.is_special_batch) ? globalThis.Number(object.is_special_batch) : undefined,
      float_sc_resource_id: isSet(object.float_sc_resource_id)
        ? globalThis.Number(object.float_sc_resource_id)
        : undefined,
      tag_image: isSet(object.tag_image) ? globalThis.String(object.tag_image) : undefined,
      crit_prob: isSet(object.crit_prob) ? globalThis.Number(object.crit_prob) : undefined,
      rcost: isSet(object.rcost) ? globalThis.Number(object.rcost) : 0,
      test: isSet(object.test) ? globalThis.Number(object.test) : undefined,
      face_effect_type: isSet(object.face_effect_type) ? globalThis.Number(object.face_effect_type) : undefined,
      face_effect_id: isSet(object.face_effect_id) ? globalThis.Number(object.face_effect_id) : undefined,
      is_naming: isSet(object.is_naming) ? globalThis.Boolean(object.is_naming) : undefined,
      receive_user_info: isSet(object.receive_user_info)
        ? SendGiftV2_GiftItem_ReceiveUserInfo.fromJSON(object.receive_user_info)
        : undefined,
      is_join_receiver: isSet(object.is_join_receiver) ? globalThis.Boolean(object.is_join_receiver) : undefined,
      bag_gift: isSet(object.bag_gift) ? SendGiftV2_GiftItem_BagGiftInfo.fromJSON(object.bag_gift) : undefined,
      gift_tag: globalThis.Array.isArray(object?.gift_tag) ? object.gift_tag.map((e: any) => globalThis.Number(e)) : [],
      receiver_uinfo: isSet(object.receiver_uinfo) ? SendGiftV2_UInfo.fromJSON(object.receiver_uinfo) : undefined,
      face_effect_v2: isSet(object.face_effect_v2)
        ? SendGiftV2_GiftItem_FaceEffectV2.fromJSON(object.face_effect_v2)
        : undefined,
      gift_info: isSet(object.gift_info)
        ? SendGiftV2_GiftItem_GiftMaterialSnapShot.fromJSON(object.gift_info)
        : undefined,
      gift_tip_price: isSet(object.gift_tip_price) ? globalThis.Number(object.gift_tip_price) : undefined,
    };
  },

  toJSON(message: SendGiftV2_GiftItem): unknown {
    const obj: any = {};
    if (message.gift_id !== 0) {
      obj.gift_id = Math.round(message.gift_id);
    }
    if (message.gift_name !== "") {
      obj.gift_name = message.gift_name;
    }
    if (message.num !== 0) {
      obj.num = Math.round(message.num);
    }
    if (message.demarcation !== 0) {
      obj.demarcation = Math.round(message.demarcation);
    }
    if (message.price !== 0) {
      obj.price = Math.round(message.price);
    }
    if (message.discount_price !== 0) {
      obj.discount_price = Math.round(message.discount_price);
    }
    if (message.total_coin !== 0) {
      obj.total_coin = Math.round(message.total_coin);
    }
    if (message.coin_type !== "") {
      obj.coin_type = message.coin_type;
    }
    if (message.tid !== "") {
      obj.tid = message.tid;
    }
    if (message.timestamp !== 0) {
      obj.timestamp = Math.round(message.timestamp);
    }
    if (message.super_batch_gift_num !== 0) {
      obj.super_batch_gift_num = Math.round(message.super_batch_gift_num);
    }
    if (message.batch_combo_id !== "") {
      obj.batch_combo_id = message.batch_combo_id;
    }
    if (message.combo_resources_id !== 0) {
      obj.combo_resources_id = Math.round(message.combo_resources_id);
    }
    if (message.combo_total_coin !== 0) {
      obj.combo_total_coin = Math.round(message.combo_total_coin);
    }
    if (message.combo_stay_time !== 0) {
      obj.combo_stay_time = Math.round(message.combo_stay_time);
    }
    if (message.magnification !== 0) {
      obj.magnification = message.magnification;
    }
    if (message.show_batch_combo_send !== undefined) {
      obj.show_batch_combo_send = message.show_batch_combo_send;
    }
    if (message.action !== "") {
      obj.action = message.action;
    }
    if (message.effect_block !== undefined) {
      obj.effect_block = Math.round(message.effect_block);
    }
    if (message.is_special_batch !== undefined) {
      obj.is_special_batch = Math.round(message.is_special_batch);
    }
    if (message.float_sc_resource_id !== undefined) {
      obj.float_sc_resource_id = Math.round(message.float_sc_resource_id);
    }
    if (message.tag_image !== undefined) {
      obj.tag_image = message.tag_image;
    }
    if (message.crit_prob !== undefined) {
      obj.crit_prob = Math.round(message.crit_prob);
    }
    if (message.rcost !== 0) {
      obj.rcost = Math.round(message.rcost);
    }
    if (message.test !== undefined) {
      obj.test = Math.round(message.test);
    }
    if (message.face_effect_type !== undefined) {
      obj.face_effect_type = Math.round(message.face_effect_type);
    }
    if (message.face_effect_id !== undefined) {
      obj.face_effect_id = Math.round(message.face_effect_id);
    }
    if (message.is_naming !== undefined) {
      obj.is_naming = message.is_naming;
    }
    if (message.receive_user_info !== undefined) {
      obj.receive_user_info = SendGiftV2_GiftItem_ReceiveUserInfo.toJSON(message.receive_user_info);
    }
    if (message.is_join_receiver !== undefined) {
      obj.is_join_receiver = message.is_join_receiver;
    }
    if (message.bag_gift !== undefined) {
      obj.bag_gift = SendGiftV2_GiftItem_BagGiftInfo.toJSON(message.bag_gift);
    }
    if (message.gift_tag?.length) {
      obj.gift_tag = message.gift_tag.map((e) => Math.round(e));
    }
    if (message.receiver_uinfo !== undefined) {
      obj.receiver_uinfo = SendGiftV2_UInfo.toJSON(message.receiver_uinfo);
    }
    if (message.face_effect_v2 !== undefined) {
      obj.face_effect_v2 = SendGiftV2_GiftItem_FaceEffectV2.toJSON(message.face_effect_v2);
    }
    if (message.gift_info !== undefined) {
      obj.gift_info = SendGiftV2_GiftItem_GiftMaterialSnapShot.toJSON(message.gift_info);
    }
    if (message.gift_tip_price !== undefined) {
      obj.gift_tip_price = Math.round(message.gift_tip_price);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SendGiftV2_GiftItem>, I>>(base?: I): SendGiftV2_GiftItem {
    return SendGiftV2_GiftItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGiftV2_GiftItem>, I>>(object: I): SendGiftV2_GiftItem {
    const message = createBaseSendGiftV2_GiftItem();
    message.gift_id = object.gift_id ?? 0;
    message.gift_name = object.gift_name ?? "";
    message.num = object.num ?? 0;
    message.demarcation = object.demarcation ?? 0;
    message.price = object.price ?? 0;
    message.discount_price = object.discount_price ?? 0;
    message.total_coin = object.total_coin ?? 0;
    message.coin_type = object.coin_type ?? "";
    message.tid = object.tid ?? "";
    message.timestamp = object.timestamp ?? 0;
    message.super_batch_gift_num = object.super_batch_gift_num ?? 0;
    message.batch_combo_id = object.batch_combo_id ?? "";
    message.combo_resources_id = object.combo_resources_id ?? 0;
    message.combo_total_coin = object.combo_total_coin ?? 0;
    message.combo_stay_time = object.combo_stay_time ?? 0;
    message.magnification = object.magnification ?? 0;
    message.show_batch_combo_send = object.show_batch_combo_send ?? undefined;
    message.action = object.action ?? "";
    message.effect_block = object.effect_block ?? undefined;
    message.is_special_batch = object.is_special_batch ?? undefined;
    message.float_sc_resource_id = object.float_sc_resource_id ?? undefined;
    message.tag_image = object.tag_image ?? undefined;
    message.crit_prob = object.crit_prob ?? undefined;
    message.rcost = object.rcost ?? 0;
    message.test = object.test ?? undefined;
    message.face_effect_type = object.face_effect_type ?? undefined;
    message.face_effect_id = object.face_effect_id ?? undefined;
    message.is_naming = object.is_naming ?? undefined;
    message.receive_user_info = (object.receive_user_info !== undefined && object.receive_user_info !== null)
      ? SendGiftV2_GiftItem_ReceiveUserInfo.fromPartial(object.receive_user_info)
      : undefined;
    message.is_join_receiver = object.is_join_receiver ?? undefined;
    message.bag_gift = (object.bag_gift !== undefined && object.bag_gift !== null)
      ? SendGiftV2_GiftItem_BagGiftInfo.fromPartial(object.bag_gift)
      : undefined;
    message.gift_tag = object.gift_tag?.map((e) => e) || [];
    message.receiver_uinfo = (object.receiver_uinfo !== undefined && object.receiver_uinfo !== null)
      ? SendGiftV2_UInfo.fromPartial(object.receiver_uinfo)
      : undefined;
    message.face_effect_v2 = (object.face_effect_v2 !== undefined && object.face_effect_v2 !== null)
      ? SendGiftV2_GiftItem_FaceEffectV2.fromPartial(object.face_effect_v2)
      : undefined;
    message.gift_info = (object.gift_info !== undefined && object.gift_info !== null)
      ? SendGiftV2_GiftItem_GiftMaterialSnapShot.fromPartial(object.gift_info)
      : undefined;
    message.gift_tip_price = object.gift_tip_price ?? undefined;
    return message;
  },
};

function createBaseSendGiftV2_GiftItem_ReceiveUserInfo(): SendGiftV2_GiftItem_ReceiveUserInfo {
  return { uname: "", uid: 0 };
}

export const SendGiftV2_GiftItem_ReceiveUserInfo: MessageFns<SendGiftV2_GiftItem_ReceiveUserInfo> = {
  encode(message: SendGiftV2_GiftItem_ReceiveUserInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.uname !== "") {
      writer.uint32(10).string(message.uname);
    }
    if (message.uid !== 0) {
      writer.uint32(16).uint64(message.uid);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendGiftV2_GiftItem_ReceiveUserInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendGiftV2_GiftItem_ReceiveUserInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.uname = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.uid = longToNumber(reader.uint64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SendGiftV2_GiftItem_ReceiveUserInfo {
    return {
      uname: isSet(object.uname) ? globalThis.String(object.uname) : "",
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
    };
  },

  toJSON(message: SendGiftV2_GiftItem_ReceiveUserInfo): unknown {
    const obj: any = {};
    if (message.uname !== "") {
      obj.uname = message.uname;
    }
    if (message.uid !== 0) {
      obj.uid = Math.round(message.uid);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SendGiftV2_GiftItem_ReceiveUserInfo>, I>>(
    base?: I,
  ): SendGiftV2_GiftItem_ReceiveUserInfo {
    return SendGiftV2_GiftItem_ReceiveUserInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGiftV2_GiftItem_ReceiveUserInfo>, I>>(
    object: I,
  ): SendGiftV2_GiftItem_ReceiveUserInfo {
    const message = createBaseSendGiftV2_GiftItem_ReceiveUserInfo();
    message.uname = object.uname ?? "";
    message.uid = object.uid ?? 0;
    return message;
  },
};

function createBaseSendGiftV2_GiftItem_BagGiftInfo(): SendGiftV2_GiftItem_BagGiftInfo {
  return { show_price: 0, price_for_show: 0 };
}

export const SendGiftV2_GiftItem_BagGiftInfo: MessageFns<SendGiftV2_GiftItem_BagGiftInfo> = {
  encode(message: SendGiftV2_GiftItem_BagGiftInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.show_price !== 0) {
      writer.uint32(8).int32(message.show_price);
    }
    if (message.price_for_show !== 0) {
      writer.uint32(16).int64(message.price_for_show);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendGiftV2_GiftItem_BagGiftInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendGiftV2_GiftItem_BagGiftInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.show_price = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.price_for_show = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SendGiftV2_GiftItem_BagGiftInfo {
    return {
      show_price: isSet(object.show_price) ? globalThis.Number(object.show_price) : 0,
      price_for_show: isSet(object.price_for_show) ? globalThis.Number(object.price_for_show) : 0,
    };
  },

  toJSON(message: SendGiftV2_GiftItem_BagGiftInfo): unknown {
    const obj: any = {};
    if (message.show_price !== 0) {
      obj.show_price = Math.round(message.show_price);
    }
    if (message.price_for_show !== 0) {
      obj.price_for_show = Math.round(message.price_for_show);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SendGiftV2_GiftItem_BagGiftInfo>, I>>(base?: I): SendGiftV2_GiftItem_BagGiftInfo {
    return SendGiftV2_GiftItem_BagGiftInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGiftV2_GiftItem_BagGiftInfo>, I>>(
    object: I,
  ): SendGiftV2_GiftItem_BagGiftInfo {
    const message = createBaseSendGiftV2_GiftItem_BagGiftInfo();
    message.show_price = object.show_price ?? 0;
    message.price_for_show = object.price_for_show ?? 0;
    return message;
  },
};

function createBaseSendGiftV2_GiftItem_FaceEffectV2(): SendGiftV2_GiftItem_FaceEffectV2 {
  return { id: 0, type: 0 };
}

export const SendGiftV2_GiftItem_FaceEffectV2: MessageFns<SendGiftV2_GiftItem_FaceEffectV2> = {
  encode(message: SendGiftV2_GiftItem_FaceEffectV2, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    if (message.type !== 0) {
      writer.uint32(16).int64(message.type);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendGiftV2_GiftItem_FaceEffectV2 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendGiftV2_GiftItem_FaceEffectV2();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.type = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SendGiftV2_GiftItem_FaceEffectV2 {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      type: isSet(object.type) ? globalThis.Number(object.type) : 0,
    };
  },

  toJSON(message: SendGiftV2_GiftItem_FaceEffectV2): unknown {
    const obj: any = {};
    if (message.id !== 0) {
      obj.id = Math.round(message.id);
    }
    if (message.type !== 0) {
      obj.type = Math.round(message.type);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SendGiftV2_GiftItem_FaceEffectV2>, I>>(
    base?: I,
  ): SendGiftV2_GiftItem_FaceEffectV2 {
    return SendGiftV2_GiftItem_FaceEffectV2.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGiftV2_GiftItem_FaceEffectV2>, I>>(
    object: I,
  ): SendGiftV2_GiftItem_FaceEffectV2 {
    const message = createBaseSendGiftV2_GiftItem_FaceEffectV2();
    message.id = object.id ?? 0;
    message.type = object.type ?? 0;
    return message;
  },
};

function createBaseSendGiftV2_GiftItem_GiftMaterialSnapShot(): SendGiftV2_GiftItem_GiftMaterialSnapShot {
  return { img_basic: "", webp: "", effect_id: 0, has_imaged_gift: 0, gif: undefined };
}

export const SendGiftV2_GiftItem_GiftMaterialSnapShot: MessageFns<SendGiftV2_GiftItem_GiftMaterialSnapShot> = {
  encode(message: SendGiftV2_GiftItem_GiftMaterialSnapShot, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.img_basic !== "") {
      writer.uint32(10).string(message.img_basic);
    }
    if (message.webp !== "") {
      writer.uint32(18).string(message.webp);
    }
    if (message.effect_id !== 0) {
      writer.uint32(24).int64(message.effect_id);
    }
    if (message.has_imaged_gift !== 0) {
      writer.uint32(32).int64(message.has_imaged_gift);
    }
    if (message.gif !== undefined) {
      writer.uint32(42).string(message.gif);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendGiftV2_GiftItem_GiftMaterialSnapShot {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendGiftV2_GiftItem_GiftMaterialSnapShot();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.img_basic = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.webp = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.effect_id = longToNumber(reader.int64());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.has_imaged_gift = longToNumber(reader.int64());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.gif = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SendGiftV2_GiftItem_GiftMaterialSnapShot {
    return {
      img_basic: isSet(object.img_basic) ? globalThis.String(object.img_basic) : "",
      webp: isSet(object.webp) ? globalThis.String(object.webp) : "",
      effect_id: isSet(object.effect_id) ? globalThis.Number(object.effect_id) : 0,
      has_imaged_gift: isSet(object.has_imaged_gift) ? globalThis.Number(object.has_imaged_gift) : 0,
      gif: isSet(object.gif) ? globalThis.String(object.gif) : undefined,
    };
  },

  toJSON(message: SendGiftV2_GiftItem_GiftMaterialSnapShot): unknown {
    const obj: any = {};
    if (message.img_basic !== "") {
      obj.img_basic = message.img_basic;
    }
    if (message.webp !== "") {
      obj.webp = message.webp;
    }
    if (message.effect_id !== 0) {
      obj.effect_id = Math.round(message.effect_id);
    }
    if (message.has_imaged_gift !== 0) {
      obj.has_imaged_gift = Math.round(message.has_imaged_gift);
    }
    if (message.gif !== undefined) {
      obj.gif = message.gif;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SendGiftV2_GiftItem_GiftMaterialSnapShot>, I>>(
    base?: I,
  ): SendGiftV2_GiftItem_GiftMaterialSnapShot {
    return SendGiftV2_GiftItem_GiftMaterialSnapShot.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGiftV2_GiftItem_GiftMaterialSnapShot>, I>>(
    object: I,
  ): SendGiftV2_GiftItem_GiftMaterialSnapShot {
    const message = createBaseSendGiftV2_GiftItem_GiftMaterialSnapShot();
    message.img_basic = object.img_basic ?? "";
    message.webp = object.webp ?? "";
    message.effect_id = object.effect_id ?? 0;
    message.has_imaged_gift = object.has_imaged_gift ?? 0;
    message.gif = object.gif ?? undefined;
    return message;
  },
};

function createBaseSendGiftV2_GroupMedal(): SendGiftV2_GroupMedal {
  return { medal_id: 0, name: "", is_lighted: 0 };
}

export const SendGiftV2_GroupMedal: MessageFns<SendGiftV2_GroupMedal> = {
  encode(message: SendGiftV2_GroupMedal, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.medal_id !== 0) {
      writer.uint32(8).int64(message.medal_id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.is_lighted !== 0) {
      writer.uint32(24).int64(message.is_lighted);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendGiftV2_GroupMedal {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendGiftV2_GroupMedal();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.medal_id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.is_lighted = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SendGiftV2_GroupMedal {
    return {
      medal_id: isSet(object.medal_id) ? globalThis.Number(object.medal_id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      is_lighted: isSet(object.is_lighted) ? globalThis.Number(object.is_lighted) : 0,
    };
  },

  toJSON(message: SendGiftV2_GroupMedal): unknown {
    const obj: any = {};
    if (message.medal_id !== 0) {
      obj.medal_id = Math.round(message.medal_id);
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.is_lighted !== 0) {
      obj.is_lighted = Math.round(message.is_lighted);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SendGiftV2_GroupMedal>, I>>(base?: I): SendGiftV2_GroupMedal {
    return SendGiftV2_GroupMedal.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGiftV2_GroupMedal>, I>>(object: I): SendGiftV2_GroupMedal {
    const message = createBaseSendGiftV2_GroupMedal();
    message.medal_id = object.medal_id ?? 0;
    message.name = object.name ?? "";
    message.is_lighted = object.is_lighted ?? 0;
    return message;
  },
};

function createBaseInteractWordV2(): InteractWordV2 {
  return {
    uid: 0,
    username: "",
    msg_type: 0,
    room_id: 0,
    timestamp: 0,
    timestamp_ms: 0,
    medal: undefined,
    trigger_time: 0,
    guard_type: 0,
    f17: 0,
    uinfo: undefined,
    relation_tail: undefined,
  };
}

export const InteractWordV2: MessageFns<InteractWordV2> = {
  encode(message: InteractWordV2, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.uid !== 0) {
      writer.uint32(8).uint64(message.uid);
    }
    if (message.username !== "") {
      writer.uint32(18).string(message.username);
    }
    if (message.msg_type !== 0) {
      writer.uint32(40).uint32(message.msg_type);
    }
    if (message.room_id !== 0) {
      writer.uint32(48).uint32(message.room_id);
    }
    if (message.timestamp !== 0) {
      writer.uint32(56).uint32(message.timestamp);
    }
    if (message.timestamp_ms !== 0) {
      writer.uint32(64).uint64(message.timestamp_ms);
    }
    if (message.medal !== undefined) {
      InteractWordV2_Medal.encode(message.medal, writer.uint32(74).fork()).join();
    }
    if (message.trigger_time !== 0) {
      writer.uint32(120).uint64(message.trigger_time);
    }
    if (message.guard_type !== 0) {
      writer.uint32(128).uint32(message.guard_type);
    }
    if (message.f17 !== 0) {
      writer.uint32(136).int32(message.f17);
    }
    if (message.uinfo !== undefined) {
      InteractWordV2_UInfo.encode(message.uinfo, writer.uint32(178).fork()).join();
    }
    if (message.relation_tail !== undefined) {
      InteractWordV2_RelationTail.encode(message.relation_tail, writer.uint32(186).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InteractWordV2 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInteractWordV2();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.uid = longToNumber(reader.uint64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.username = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.msg_type = reader.uint32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.room_id = reader.uint32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.timestamp = reader.uint32();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.timestamp_ms = longToNumber(reader.uint64());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.medal = InteractWordV2_Medal.decode(reader, reader.uint32());
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.trigger_time = longToNumber(reader.uint64());
          continue;
        }
        case 16: {
          if (tag !== 128) {
            break;
          }

          message.guard_type = reader.uint32();
          continue;
        }
        case 17: {
          if (tag !== 136) {
            break;
          }

          message.f17 = reader.int32();
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.uinfo = InteractWordV2_UInfo.decode(reader, reader.uint32());
          continue;
        }
        case 23: {
          if (tag !== 186) {
            break;
          }

          message.relation_tail = InteractWordV2_RelationTail.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InteractWordV2 {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      username: isSet(object.username) ? globalThis.String(object.username) : "",
      msg_type: isSet(object.msg_type) ? globalThis.Number(object.msg_type) : 0,
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      timestamp_ms: isSet(object.timestamp_ms) ? globalThis.Number(object.timestamp_ms) : 0,
      medal: isSet(object.medal) ? InteractWordV2_Medal.fromJSON(object.medal) : undefined,
      trigger_time: isSet(object.trigger_time) ? globalThis.Number(object.trigger_time) : 0,
      guard_type: isSet(object.guard_type) ? globalThis.Number(object.guard_type) : 0,
      f17: isSet(object.f17) ? globalThis.Number(object.f17) : 0,
      uinfo: isSet(object.uinfo) ? InteractWordV2_UInfo.fromJSON(object.uinfo) : undefined,
      relation_tail: isSet(object.relation_tail)
        ? InteractWordV2_RelationTail.fromJSON(object.relation_tail)
        : undefined,
    };
  },

  toJSON(message: InteractWordV2): unknown {
    const obj: any = {};
    if (message.uid !== 0) {
      obj.uid = Math.round(message.uid);
    }
    if (message.username !== "") {
      obj.username = message.username;
    }
    if (message.msg_type !== 0) {
      obj.msg_type = Math.round(message.msg_type);
    }
    if (message.room_id !== 0) {
      obj.room_id = Math.round(message.room_id);
    }
    if (message.timestamp !== 0) {
      obj.timestamp = Math.round(message.timestamp);
    }
    if (message.timestamp_ms !== 0) {
      obj.timestamp_ms = Math.round(message.timestamp_ms);
    }
    if (message.medal !== undefined) {
      obj.medal = InteractWordV2_Medal.toJSON(message.medal);
    }
    if (message.trigger_time !== 0) {
      obj.trigger_time = Math.round(message.trigger_time);
    }
    if (message.guard_type !== 0) {
      obj.guard_type = Math.round(message.guard_type);
    }
    if (message.f17 !== 0) {
      obj.f17 = Math.round(message.f17);
    }
    if (message.uinfo !== undefined) {
      obj.uinfo = InteractWordV2_UInfo.toJSON(message.uinfo);
    }
    if (message.relation_tail !== undefined) {
      obj.relation_tail = InteractWordV2_RelationTail.toJSON(message.relation_tail);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InteractWordV2>, I>>(base?: I): InteractWordV2 {
    return InteractWordV2.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InteractWordV2>, I>>(object: I): InteractWordV2 {
    const message = createBaseInteractWordV2();
    message.uid = object.uid ?? 0;
    message.username = object.username ?? "";
    message.msg_type = object.msg_type ?? 0;
    message.room_id = object.room_id ?? 0;
    message.timestamp = object.timestamp ?? 0;
    message.timestamp_ms = object.timestamp_ms ?? 0;
    message.medal = (object.medal !== undefined && object.medal !== null)
      ? InteractWordV2_Medal.fromPartial(object.medal)
      : undefined;
    message.trigger_time = object.trigger_time ?? 0;
    message.guard_type = object.guard_type ?? 0;
    message.f17 = object.f17 ?? 0;
    message.uinfo = (object.uinfo !== undefined && object.uinfo !== null)
      ? InteractWordV2_UInfo.fromPartial(object.uinfo)
      : undefined;
    message.relation_tail = (object.relation_tail !== undefined && object.relation_tail !== null)
      ? InteractWordV2_RelationTail.fromPartial(object.relation_tail)
      : undefined;
    return message;
  },
};

function createBaseInteractWordV2_Medal(): InteractWordV2_Medal {
  return { ruid: 0, level: 0, name: "", f4: 0, f5: 0, f6: 0, f7: 0, is_lighted: 0, guard_level: 0, room_id: 0, f13: 0 };
}

export const InteractWordV2_Medal: MessageFns<InteractWordV2_Medal> = {
  encode(message: InteractWordV2_Medal, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ruid !== 0) {
      writer.uint32(8).uint64(message.ruid);
    }
    if (message.level !== 0) {
      writer.uint32(16).uint32(message.level);
    }
    if (message.name !== "") {
      writer.uint32(26).string(message.name);
    }
    if (message.f4 !== 0) {
      writer.uint32(32).int32(message.f4);
    }
    if (message.f5 !== 0) {
      writer.uint32(40).int32(message.f5);
    }
    if (message.f6 !== 0) {
      writer.uint32(48).int32(message.f6);
    }
    if (message.f7 !== 0) {
      writer.uint32(56).int32(message.f7);
    }
    if (message.is_lighted !== 0) {
      writer.uint32(64).uint32(message.is_lighted);
    }
    if (message.guard_level !== 0) {
      writer.uint32(72).uint32(message.guard_level);
    }
    if (message.room_id !== 0) {
      writer.uint32(96).uint32(message.room_id);
    }
    if (message.f13 !== 0) {
      writer.uint32(104).int32(message.f13);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InteractWordV2_Medal {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInteractWordV2_Medal();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.ruid = longToNumber(reader.uint64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.level = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.f4 = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.f5 = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.f6 = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.f7 = reader.int32();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.is_lighted = reader.uint32();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.guard_level = reader.uint32();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.room_id = reader.uint32();
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.f13 = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InteractWordV2_Medal {
    return {
      ruid: isSet(object.ruid) ? globalThis.Number(object.ruid) : 0,
      level: isSet(object.level) ? globalThis.Number(object.level) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      f4: isSet(object.f4) ? globalThis.Number(object.f4) : 0,
      f5: isSet(object.f5) ? globalThis.Number(object.f5) : 0,
      f6: isSet(object.f6) ? globalThis.Number(object.f6) : 0,
      f7: isSet(object.f7) ? globalThis.Number(object.f7) : 0,
      is_lighted: isSet(object.is_lighted) ? globalThis.Number(object.is_lighted) : 0,
      guard_level: isSet(object.guard_level) ? globalThis.Number(object.guard_level) : 0,
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      f13: isSet(object.f13) ? globalThis.Number(object.f13) : 0,
    };
  },

  toJSON(message: InteractWordV2_Medal): unknown {
    const obj: any = {};
    if (message.ruid !== 0) {
      obj.ruid = Math.round(message.ruid);
    }
    if (message.level !== 0) {
      obj.level = Math.round(message.level);
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.f4 !== 0) {
      obj.f4 = Math.round(message.f4);
    }
    if (message.f5 !== 0) {
      obj.f5 = Math.round(message.f5);
    }
    if (message.f6 !== 0) {
      obj.f6 = Math.round(message.f6);
    }
    if (message.f7 !== 0) {
      obj.f7 = Math.round(message.f7);
    }
    if (message.is_lighted !== 0) {
      obj.is_lighted = Math.round(message.is_lighted);
    }
    if (message.guard_level !== 0) {
      obj.guard_level = Math.round(message.guard_level);
    }
    if (message.room_id !== 0) {
      obj.room_id = Math.round(message.room_id);
    }
    if (message.f13 !== 0) {
      obj.f13 = Math.round(message.f13);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InteractWordV2_Medal>, I>>(base?: I): InteractWordV2_Medal {
    return InteractWordV2_Medal.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InteractWordV2_Medal>, I>>(object: I): InteractWordV2_Medal {
    const message = createBaseInteractWordV2_Medal();
    message.ruid = object.ruid ?? 0;
    message.level = object.level ?? 0;
    message.name = object.name ?? "";
    message.f4 = object.f4 ?? 0;
    message.f5 = object.f5 ?? 0;
    message.f6 = object.f6 ?? 0;
    message.f7 = object.f7 ?? 0;
    message.is_lighted = object.is_lighted ?? 0;
    message.guard_level = object.guard_level ?? 0;
    message.room_id = object.room_id ?? 0;
    message.f13 = object.f13 ?? 0;
    return message;
  },
};

function createBaseInteractWordV2_UInfo(): InteractWordV2_UInfo {
  return { uid: 0, base: undefined, medal: undefined, wealth: undefined, f6: undefined };
}

export const InteractWordV2_UInfo: MessageFns<InteractWordV2_UInfo> = {
  encode(message: InteractWordV2_UInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.uid !== 0) {
      writer.uint32(8).uint64(message.uid);
    }
    if (message.base !== undefined) {
      InteractWordV2_UInfo_Base.encode(message.base, writer.uint32(18).fork()).join();
    }
    if (message.medal !== undefined) {
      InteractWordV2_UInfo_Medal.encode(message.medal, writer.uint32(26).fork()).join();
    }
    if (message.wealth !== undefined) {
      InteractWordV2_UInfo_Wealth.encode(message.wealth, writer.uint32(34).fork()).join();
    }
    if (message.f6 !== undefined) {
      InteractWordV2_UInfo_Message6.encode(message.f6, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InteractWordV2_UInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInteractWordV2_UInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.uid = longToNumber(reader.uint64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.base = InteractWordV2_UInfo_Base.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.medal = InteractWordV2_UInfo_Medal.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.wealth = InteractWordV2_UInfo_Wealth.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.f6 = InteractWordV2_UInfo_Message6.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InteractWordV2_UInfo {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      base: isSet(object.base) ? InteractWordV2_UInfo_Base.fromJSON(object.base) : undefined,
      medal: isSet(object.medal) ? InteractWordV2_UInfo_Medal.fromJSON(object.medal) : undefined,
      wealth: isSet(object.wealth) ? InteractWordV2_UInfo_Wealth.fromJSON(object.wealth) : undefined,
      f6: isSet(object.f6) ? InteractWordV2_UInfo_Message6.fromJSON(object.f6) : undefined,
    };
  },

  toJSON(message: InteractWordV2_UInfo): unknown {
    const obj: any = {};
    if (message.uid !== 0) {
      obj.uid = Math.round(message.uid);
    }
    if (message.base !== undefined) {
      obj.base = InteractWordV2_UInfo_Base.toJSON(message.base);
    }
    if (message.medal !== undefined) {
      obj.medal = InteractWordV2_UInfo_Medal.toJSON(message.medal);
    }
    if (message.wealth !== undefined) {
      obj.wealth = InteractWordV2_UInfo_Wealth.toJSON(message.wealth);
    }
    if (message.f6 !== undefined) {
      obj.f6 = InteractWordV2_UInfo_Message6.toJSON(message.f6);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InteractWordV2_UInfo>, I>>(base?: I): InteractWordV2_UInfo {
    return InteractWordV2_UInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InteractWordV2_UInfo>, I>>(object: I): InteractWordV2_UInfo {
    const message = createBaseInteractWordV2_UInfo();
    message.uid = object.uid ?? 0;
    message.base = (object.base !== undefined && object.base !== null)
      ? InteractWordV2_UInfo_Base.fromPartial(object.base)
      : undefined;
    message.medal = (object.medal !== undefined && object.medal !== null)
      ? InteractWordV2_UInfo_Medal.fromPartial(object.medal)
      : undefined;
    message.wealth = (object.wealth !== undefined && object.wealth !== null)
      ? InteractWordV2_UInfo_Wealth.fromPartial(object.wealth)
      : undefined;
    message.f6 = (object.f6 !== undefined && object.f6 !== null)
      ? InteractWordV2_UInfo_Message6.fromPartial(object.f6)
      : undefined;
    return message;
  },
};

function createBaseInteractWordV2_UInfo_Base(): InteractWordV2_UInfo_Base {
  return { username: "", avatar: "", f3: "" };
}

export const InteractWordV2_UInfo_Base: MessageFns<InteractWordV2_UInfo_Base> = {
  encode(message: InteractWordV2_UInfo_Base, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.username !== "") {
      writer.uint32(10).string(message.username);
    }
    if (message.avatar !== "") {
      writer.uint32(18).string(message.avatar);
    }
    if (message.f3 !== "") {
      writer.uint32(26).string(message.f3);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InteractWordV2_UInfo_Base {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInteractWordV2_UInfo_Base();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.username = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.avatar = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.f3 = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InteractWordV2_UInfo_Base {
    return {
      username: isSet(object.username) ? globalThis.String(object.username) : "",
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : "",
      f3: isSet(object.f3) ? globalThis.String(object.f3) : "",
    };
  },

  toJSON(message: InteractWordV2_UInfo_Base): unknown {
    const obj: any = {};
    if (message.username !== "") {
      obj.username = message.username;
    }
    if (message.avatar !== "") {
      obj.avatar = message.avatar;
    }
    if (message.f3 !== "") {
      obj.f3 = message.f3;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InteractWordV2_UInfo_Base>, I>>(base?: I): InteractWordV2_UInfo_Base {
    return InteractWordV2_UInfo_Base.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InteractWordV2_UInfo_Base>, I>>(object: I): InteractWordV2_UInfo_Base {
    const message = createBaseInteractWordV2_UInfo_Base();
    message.username = object.username ?? "";
    message.avatar = object.avatar ?? "";
    message.f3 = object.f3 ?? "";
    return message;
  },
};

function createBaseInteractWordV2_UInfo_Medal(): InteractWordV2_UInfo_Medal {
  return {
    name: "",
    level: 0,
    color_start: 0,
    color_end: 0,
    color_border: 0,
    color: 0,
    id: 0,
    f9: 0,
    ruid: 0,
    f11: 0,
    f12: 0,
    guard_icon: undefined,
    v2_medal_color_start: 0,
    v2_medal_color_end: 0,
    v2_medal_color_border: 0,
    v2_medal_text: 0,
    v2_medal_level: 0,
  };
}

export const InteractWordV2_UInfo_Medal: MessageFns<InteractWordV2_UInfo_Medal> = {
  encode(message: InteractWordV2_UInfo_Medal, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.level !== 0) {
      writer.uint32(16).uint32(message.level);
    }
    if (message.color_start !== 0) {
      writer.uint32(24).int32(message.color_start);
    }
    if (message.color_end !== 0) {
      writer.uint32(32).int32(message.color_end);
    }
    if (message.color_border !== 0) {
      writer.uint32(40).int32(message.color_border);
    }
    if (message.color !== 0) {
      writer.uint32(48).int32(message.color);
    }
    if (message.id !== 0) {
      writer.uint32(56).int32(message.id);
    }
    if (message.f9 !== 0) {
      writer.uint32(72).int32(message.f9);
    }
    if (message.ruid !== 0) {
      writer.uint32(80).uint64(message.ruid);
    }
    if (message.f11 !== 0) {
      writer.uint32(88).int32(message.f11);
    }
    if (message.f12 !== 0) {
      writer.uint32(96).int32(message.f12);
    }
    if (message.guard_icon !== undefined) {
      writer.uint32(106).string(message.guard_icon);
    }
    if (message.v2_medal_color_start !== 0) {
      writer.uint32(120).uint32(message.v2_medal_color_start);
    }
    if (message.v2_medal_color_end !== 0) {
      writer.uint32(128).uint32(message.v2_medal_color_end);
    }
    if (message.v2_medal_color_border !== 0) {
      writer.uint32(136).uint32(message.v2_medal_color_border);
    }
    if (message.v2_medal_text !== 0) {
      writer.uint32(144).uint32(message.v2_medal_text);
    }
    if (message.v2_medal_level !== 0) {
      writer.uint32(152).uint32(message.v2_medal_level);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InteractWordV2_UInfo_Medal {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInteractWordV2_UInfo_Medal();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.level = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.color_start = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.color_end = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.color_border = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.color = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.id = reader.int32();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.f9 = reader.int32();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.ruid = longToNumber(reader.uint64());
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.f11 = reader.int32();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.f12 = reader.int32();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.guard_icon = reader.string();
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.v2_medal_color_start = reader.uint32();
          continue;
        }
        case 16: {
          if (tag !== 128) {
            break;
          }

          message.v2_medal_color_end = reader.uint32();
          continue;
        }
        case 17: {
          if (tag !== 136) {
            break;
          }

          message.v2_medal_color_border = reader.uint32();
          continue;
        }
        case 18: {
          if (tag !== 144) {
            break;
          }

          message.v2_medal_text = reader.uint32();
          continue;
        }
        case 19: {
          if (tag !== 152) {
            break;
          }

          message.v2_medal_level = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InteractWordV2_UInfo_Medal {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      level: isSet(object.level) ? globalThis.Number(object.level) : 0,
      color_start: isSet(object.color_start) ? globalThis.Number(object.color_start) : 0,
      color_end: isSet(object.color_end) ? globalThis.Number(object.color_end) : 0,
      color_border: isSet(object.color_border) ? globalThis.Number(object.color_border) : 0,
      color: isSet(object.color) ? globalThis.Number(object.color) : 0,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      f9: isSet(object.f9) ? globalThis.Number(object.f9) : 0,
      ruid: isSet(object.ruid) ? globalThis.Number(object.ruid) : 0,
      f11: isSet(object.f11) ? globalThis.Number(object.f11) : 0,
      f12: isSet(object.f12) ? globalThis.Number(object.f12) : 0,
      guard_icon: isSet(object.guard_icon) ? globalThis.String(object.guard_icon) : undefined,
      v2_medal_color_start: isSet(object.v2_medal_color_start) ? globalThis.Number(object.v2_medal_color_start) : 0,
      v2_medal_color_end: isSet(object.v2_medal_color_end) ? globalThis.Number(object.v2_medal_color_end) : 0,
      v2_medal_color_border: isSet(object.v2_medal_color_border) ? globalThis.Number(object.v2_medal_color_border) : 0,
      v2_medal_text: isSet(object.v2_medal_text) ? globalThis.Number(object.v2_medal_text) : 0,
      v2_medal_level: isSet(object.v2_medal_level) ? globalThis.Number(object.v2_medal_level) : 0,
    };
  },

  toJSON(message: InteractWordV2_UInfo_Medal): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.level !== 0) {
      obj.level = Math.round(message.level);
    }
    if (message.color_start !== 0) {
      obj.color_start = Math.round(message.color_start);
    }
    if (message.color_end !== 0) {
      obj.color_end = Math.round(message.color_end);
    }
    if (message.color_border !== 0) {
      obj.color_border = Math.round(message.color_border);
    }
    if (message.color !== 0) {
      obj.color = Math.round(message.color);
    }
    if (message.id !== 0) {
      obj.id = Math.round(message.id);
    }
    if (message.f9 !== 0) {
      obj.f9 = Math.round(message.f9);
    }
    if (message.ruid !== 0) {
      obj.ruid = Math.round(message.ruid);
    }
    if (message.f11 !== 0) {
      obj.f11 = Math.round(message.f11);
    }
    if (message.f12 !== 0) {
      obj.f12 = Math.round(message.f12);
    }
    if (message.guard_icon !== undefined) {
      obj.guard_icon = message.guard_icon;
    }
    if (message.v2_medal_color_start !== 0) {
      obj.v2_medal_color_start = Math.round(message.v2_medal_color_start);
    }
    if (message.v2_medal_color_end !== 0) {
      obj.v2_medal_color_end = Math.round(message.v2_medal_color_end);
    }
    if (message.v2_medal_color_border !== 0) {
      obj.v2_medal_color_border = Math.round(message.v2_medal_color_border);
    }
    if (message.v2_medal_text !== 0) {
      obj.v2_medal_text = Math.round(message.v2_medal_text);
    }
    if (message.v2_medal_level !== 0) {
      obj.v2_medal_level = Math.round(message.v2_medal_level);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InteractWordV2_UInfo_Medal>, I>>(base?: I): InteractWordV2_UInfo_Medal {
    return InteractWordV2_UInfo_Medal.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InteractWordV2_UInfo_Medal>, I>>(object: I): InteractWordV2_UInfo_Medal {
    const message = createBaseInteractWordV2_UInfo_Medal();
    message.name = object.name ?? "";
    message.level = object.level ?? 0;
    message.color_start = object.color_start ?? 0;
    message.color_end = object.color_end ?? 0;
    message.color_border = object.color_border ?? 0;
    message.color = object.color ?? 0;
    message.id = object.id ?? 0;
    message.f9 = object.f9 ?? 0;
    message.ruid = object.ruid ?? 0;
    message.f11 = object.f11 ?? 0;
    message.f12 = object.f12 ?? 0;
    message.guard_icon = object.guard_icon ?? undefined;
    message.v2_medal_color_start = object.v2_medal_color_start ?? 0;
    message.v2_medal_color_end = object.v2_medal_color_end ?? 0;
    message.v2_medal_color_border = object.v2_medal_color_border ?? 0;
    message.v2_medal_text = object.v2_medal_text ?? 0;
    message.v2_medal_level = object.v2_medal_level ?? 0;
    return message;
  },
};

function createBaseInteractWordV2_UInfo_Wealth(): InteractWordV2_UInfo_Wealth {
  return { level: 0, icon: undefined };
}

export const InteractWordV2_UInfo_Wealth: MessageFns<InteractWordV2_UInfo_Wealth> = {
  encode(message: InteractWordV2_UInfo_Wealth, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.level !== 0) {
      writer.uint32(8).uint32(message.level);
    }
    if (message.icon !== undefined) {
      writer.uint32(18).string(message.icon);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InteractWordV2_UInfo_Wealth {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInteractWordV2_UInfo_Wealth();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.level = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.icon = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InteractWordV2_UInfo_Wealth {
    return {
      level: isSet(object.level) ? globalThis.Number(object.level) : 0,
      icon: isSet(object.icon) ? globalThis.String(object.icon) : undefined,
    };
  },

  toJSON(message: InteractWordV2_UInfo_Wealth): unknown {
    const obj: any = {};
    if (message.level !== 0) {
      obj.level = Math.round(message.level);
    }
    if (message.icon !== undefined) {
      obj.icon = message.icon;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InteractWordV2_UInfo_Wealth>, I>>(base?: I): InteractWordV2_UInfo_Wealth {
    return InteractWordV2_UInfo_Wealth.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InteractWordV2_UInfo_Wealth>, I>>(object: I): InteractWordV2_UInfo_Wealth {
    const message = createBaseInteractWordV2_UInfo_Wealth();
    message.level = object.level ?? 0;
    message.icon = object.icon ?? undefined;
    return message;
  },
};

function createBaseInteractWordV2_UInfo_Message6(): InteractWordV2_UInfo_Message6 {
  return { f1: 0, f2: "" };
}

export const InteractWordV2_UInfo_Message6: MessageFns<InteractWordV2_UInfo_Message6> = {
  encode(message: InteractWordV2_UInfo_Message6, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.f1 !== 0) {
      writer.uint32(8).int32(message.f1);
    }
    if (message.f2 !== "") {
      writer.uint32(18).string(message.f2);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InteractWordV2_UInfo_Message6 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInteractWordV2_UInfo_Message6();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.f1 = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.f2 = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InteractWordV2_UInfo_Message6 {
    return {
      f1: isSet(object.f1) ? globalThis.Number(object.f1) : 0,
      f2: isSet(object.f2) ? globalThis.String(object.f2) : "",
    };
  },

  toJSON(message: InteractWordV2_UInfo_Message6): unknown {
    const obj: any = {};
    if (message.f1 !== 0) {
      obj.f1 = Math.round(message.f1);
    }
    if (message.f2 !== "") {
      obj.f2 = message.f2;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InteractWordV2_UInfo_Message6>, I>>(base?: I): InteractWordV2_UInfo_Message6 {
    return InteractWordV2_UInfo_Message6.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InteractWordV2_UInfo_Message6>, I>>(
    object: I,
  ): InteractWordV2_UInfo_Message6 {
    const message = createBaseInteractWordV2_UInfo_Message6();
    message.f1 = object.f1 ?? 0;
    message.f2 = object.f2 ?? "";
    return message;
  },
};

function createBaseInteractWordV2_RelationTail(): InteractWordV2_RelationTail {
  return { tail_icon: "", tail_guide_text: "", tail_type: 0 };
}

export const InteractWordV2_RelationTail: MessageFns<InteractWordV2_RelationTail> = {
  encode(message: InteractWordV2_RelationTail, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.tail_icon !== "") {
      writer.uint32(10).string(message.tail_icon);
    }
    if (message.tail_guide_text !== "") {
      writer.uint32(18).string(message.tail_guide_text);
    }
    if (message.tail_type !== 0) {
      writer.uint32(24).uint32(message.tail_type);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InteractWordV2_RelationTail {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInteractWordV2_RelationTail();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tail_icon = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tail_guide_text = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.tail_type = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InteractWordV2_RelationTail {
    return {
      tail_icon: isSet(object.tail_icon) ? globalThis.String(object.tail_icon) : "",
      tail_guide_text: isSet(object.tail_guide_text) ? globalThis.String(object.tail_guide_text) : "",
      tail_type: isSet(object.tail_type) ? globalThis.Number(object.tail_type) : 0,
    };
  },

  toJSON(message: InteractWordV2_RelationTail): unknown {
    const obj: any = {};
    if (message.tail_icon !== "") {
      obj.tail_icon = message.tail_icon;
    }
    if (message.tail_guide_text !== "") {
      obj.tail_guide_text = message.tail_guide_text;
    }
    if (message.tail_type !== 0) {
      obj.tail_type = Math.round(message.tail_type);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InteractWordV2_RelationTail>, I>>(base?: I): InteractWordV2_RelationTail {
    return InteractWordV2_RelationTail.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InteractWordV2_RelationTail>, I>>(object: I): InteractWordV2_RelationTail {
    const message = createBaseInteractWordV2_RelationTail();
    message.tail_icon = object.tail_icon ?? "";
    message.tail_guide_text = object.tail_guide_text ?? "";
    message.tail_type = object.tail_type ?? 0;
    return message;
  },
};

function createBaseOnlineRankV3(): OnlineRankV3 {
  return { rank_type: "", online_list: [] };
}

export const OnlineRankV3: MessageFns<OnlineRankV3> = {
  encode(message: OnlineRankV3, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rank_type !== "") {
      writer.uint32(10).string(message.rank_type);
    }
    for (const v of message.online_list) {
      OnlineRankV3_OnlineList.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): OnlineRankV3 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOnlineRankV3();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.rank_type = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.online_list.push(OnlineRankV3_OnlineList.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OnlineRankV3 {
    return {
      rank_type: isSet(object.rank_type) ? globalThis.String(object.rank_type) : "",
      online_list: globalThis.Array.isArray(object?.online_list)
        ? object.online_list.map((e: any) => OnlineRankV3_OnlineList.fromJSON(e))
        : [],
    };
  },

  toJSON(message: OnlineRankV3): unknown {
    const obj: any = {};
    if (message.rank_type !== "") {
      obj.rank_type = message.rank_type;
    }
    if (message.online_list?.length) {
      obj.online_list = message.online_list.map((e) => OnlineRankV3_OnlineList.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OnlineRankV3>, I>>(base?: I): OnlineRankV3 {
    return OnlineRankV3.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OnlineRankV3>, I>>(object: I): OnlineRankV3 {
    const message = createBaseOnlineRankV3();
    message.rank_type = object.rank_type ?? "";
    message.online_list = object.online_list?.map((e) => OnlineRankV3_OnlineList.fromPartial(e)) || [];
    return message;
  },
};

function createBaseOnlineRankV3_OnlineList(): OnlineRankV3_OnlineList {
  return {
    uid: 0,
    face: "",
    score: "",
    uname: "",
    rank: 0,
    guard_level: undefined,
    is_mystery: undefined,
    uinfo: undefined,
  };
}

export const OnlineRankV3_OnlineList: MessageFns<OnlineRankV3_OnlineList> = {
  encode(message: OnlineRankV3_OnlineList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.uid !== 0) {
      writer.uint32(8).uint64(message.uid);
    }
    if (message.face !== "") {
      writer.uint32(18).string(message.face);
    }
    if (message.score !== "") {
      writer.uint32(26).string(message.score);
    }
    if (message.uname !== "") {
      writer.uint32(34).string(message.uname);
    }
    if (message.rank !== 0) {
      writer.uint32(40).uint32(message.rank);
    }
    if (message.guard_level !== undefined) {
      writer.uint32(48).uint32(message.guard_level);
    }
    if (message.is_mystery !== undefined) {
      writer.uint32(56).bool(message.is_mystery);
    }
    if (message.uinfo !== undefined) {
      OnlineRankV3_OnlineList_UInfo.encode(message.uinfo, writer.uint32(66).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): OnlineRankV3_OnlineList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOnlineRankV3_OnlineList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.uid = longToNumber(reader.uint64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.face = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.score = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.uname = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.rank = reader.uint32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.guard_level = reader.uint32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.is_mystery = reader.bool();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.uinfo = OnlineRankV3_OnlineList_UInfo.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OnlineRankV3_OnlineList {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      face: isSet(object.face) ? globalThis.String(object.face) : "",
      score: isSet(object.score) ? globalThis.String(object.score) : "",
      uname: isSet(object.uname) ? globalThis.String(object.uname) : "",
      rank: isSet(object.rank) ? globalThis.Number(object.rank) : 0,
      guard_level: isSet(object.guard_level) ? globalThis.Number(object.guard_level) : undefined,
      is_mystery: isSet(object.is_mystery) ? globalThis.Boolean(object.is_mystery) : undefined,
      uinfo: isSet(object.uinfo) ? OnlineRankV3_OnlineList_UInfo.fromJSON(object.uinfo) : undefined,
    };
  },

  toJSON(message: OnlineRankV3_OnlineList): unknown {
    const obj: any = {};
    if (message.uid !== 0) {
      obj.uid = Math.round(message.uid);
    }
    if (message.face !== "") {
      obj.face = message.face;
    }
    if (message.score !== "") {
      obj.score = message.score;
    }
    if (message.uname !== "") {
      obj.uname = message.uname;
    }
    if (message.rank !== 0) {
      obj.rank = Math.round(message.rank);
    }
    if (message.guard_level !== undefined) {
      obj.guard_level = Math.round(message.guard_level);
    }
    if (message.is_mystery !== undefined) {
      obj.is_mystery = message.is_mystery;
    }
    if (message.uinfo !== undefined) {
      obj.uinfo = OnlineRankV3_OnlineList_UInfo.toJSON(message.uinfo);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OnlineRankV3_OnlineList>, I>>(base?: I): OnlineRankV3_OnlineList {
    return OnlineRankV3_OnlineList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OnlineRankV3_OnlineList>, I>>(object: I): OnlineRankV3_OnlineList {
    const message = createBaseOnlineRankV3_OnlineList();
    message.uid = object.uid ?? 0;
    message.face = object.face ?? "";
    message.score = object.score ?? "";
    message.uname = object.uname ?? "";
    message.rank = object.rank ?? 0;
    message.guard_level = object.guard_level ?? undefined;
    message.is_mystery = object.is_mystery ?? undefined;
    message.uinfo = (object.uinfo !== undefined && object.uinfo !== null)
      ? OnlineRankV3_OnlineList_UInfo.fromPartial(object.uinfo)
      : undefined;
    return message;
  },
};

function createBaseOnlineRankV3_OnlineList_UInfo(): OnlineRankV3_OnlineList_UInfo {
  return { uid: 0, base: undefined, guard: undefined };
}

export const OnlineRankV3_OnlineList_UInfo: MessageFns<OnlineRankV3_OnlineList_UInfo> = {
  encode(message: OnlineRankV3_OnlineList_UInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.uid !== 0) {
      writer.uint32(8).uint64(message.uid);
    }
    if (message.base !== undefined) {
      OnlineRankV3_OnlineList_UInfo_Base.encode(message.base, writer.uint32(18).fork()).join();
    }
    if (message.guard !== undefined) {
      OnlineRankV3_OnlineList_UInfo_Guard.encode(message.guard, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): OnlineRankV3_OnlineList_UInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOnlineRankV3_OnlineList_UInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.uid = longToNumber(reader.uint64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.base = OnlineRankV3_OnlineList_UInfo_Base.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.guard = OnlineRankV3_OnlineList_UInfo_Guard.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OnlineRankV3_OnlineList_UInfo {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      base: isSet(object.base) ? OnlineRankV3_OnlineList_UInfo_Base.fromJSON(object.base) : undefined,
      guard: isSet(object.guard) ? OnlineRankV3_OnlineList_UInfo_Guard.fromJSON(object.guard) : undefined,
    };
  },

  toJSON(message: OnlineRankV3_OnlineList_UInfo): unknown {
    const obj: any = {};
    if (message.uid !== 0) {
      obj.uid = Math.round(message.uid);
    }
    if (message.base !== undefined) {
      obj.base = OnlineRankV3_OnlineList_UInfo_Base.toJSON(message.base);
    }
    if (message.guard !== undefined) {
      obj.guard = OnlineRankV3_OnlineList_UInfo_Guard.toJSON(message.guard);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OnlineRankV3_OnlineList_UInfo>, I>>(base?: I): OnlineRankV3_OnlineList_UInfo {
    return OnlineRankV3_OnlineList_UInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OnlineRankV3_OnlineList_UInfo>, I>>(
    object: I,
  ): OnlineRankV3_OnlineList_UInfo {
    const message = createBaseOnlineRankV3_OnlineList_UInfo();
    message.uid = object.uid ?? 0;
    message.base = (object.base !== undefined && object.base !== null)
      ? OnlineRankV3_OnlineList_UInfo_Base.fromPartial(object.base)
      : undefined;
    message.guard = (object.guard !== undefined && object.guard !== null)
      ? OnlineRankV3_OnlineList_UInfo_Guard.fromPartial(object.guard)
      : undefined;
    return message;
  },
};

function createBaseOnlineRankV3_OnlineList_UInfo_Base(): OnlineRankV3_OnlineList_UInfo_Base {
  return { name: "", face: "", name_color: 0, is_mystery: false };
}

export const OnlineRankV3_OnlineList_UInfo_Base: MessageFns<OnlineRankV3_OnlineList_UInfo_Base> = {
  encode(message: OnlineRankV3_OnlineList_UInfo_Base, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.face !== "") {
      writer.uint32(18).string(message.face);
    }
    if (message.name_color !== 0) {
      writer.uint32(24).uint32(message.name_color);
    }
    if (message.is_mystery !== false) {
      writer.uint32(32).bool(message.is_mystery);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): OnlineRankV3_OnlineList_UInfo_Base {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOnlineRankV3_OnlineList_UInfo_Base();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.face = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.name_color = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.is_mystery = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OnlineRankV3_OnlineList_UInfo_Base {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      face: isSet(object.face) ? globalThis.String(object.face) : "",
      name_color: isSet(object.name_color) ? globalThis.Number(object.name_color) : 0,
      is_mystery: isSet(object.is_mystery) ? globalThis.Boolean(object.is_mystery) : false,
    };
  },

  toJSON(message: OnlineRankV3_OnlineList_UInfo_Base): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.face !== "") {
      obj.face = message.face;
    }
    if (message.name_color !== 0) {
      obj.name_color = Math.round(message.name_color);
    }
    if (message.is_mystery !== false) {
      obj.is_mystery = message.is_mystery;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OnlineRankV3_OnlineList_UInfo_Base>, I>>(
    base?: I,
  ): OnlineRankV3_OnlineList_UInfo_Base {
    return OnlineRankV3_OnlineList_UInfo_Base.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OnlineRankV3_OnlineList_UInfo_Base>, I>>(
    object: I,
  ): OnlineRankV3_OnlineList_UInfo_Base {
    const message = createBaseOnlineRankV3_OnlineList_UInfo_Base();
    message.name = object.name ?? "";
    message.face = object.face ?? "";
    message.name_color = object.name_color ?? 0;
    message.is_mystery = object.is_mystery ?? false;
    return message;
  },
};

function createBaseOnlineRankV3_OnlineList_UInfo_Guard(): OnlineRankV3_OnlineList_UInfo_Guard {
  return { level: 0, expired_str: "" };
}

export const OnlineRankV3_OnlineList_UInfo_Guard: MessageFns<OnlineRankV3_OnlineList_UInfo_Guard> = {
  encode(message: OnlineRankV3_OnlineList_UInfo_Guard, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.level !== 0) {
      writer.uint32(8).uint32(message.level);
    }
    if (message.expired_str !== "") {
      writer.uint32(18).string(message.expired_str);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): OnlineRankV3_OnlineList_UInfo_Guard {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOnlineRankV3_OnlineList_UInfo_Guard();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.level = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.expired_str = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OnlineRankV3_OnlineList_UInfo_Guard {
    return {
      level: isSet(object.level) ? globalThis.Number(object.level) : 0,
      expired_str: isSet(object.expired_str) ? globalThis.String(object.expired_str) : "",
    };
  },

  toJSON(message: OnlineRankV3_OnlineList_UInfo_Guard): unknown {
    const obj: any = {};
    if (message.level !== 0) {
      obj.level = Math.round(message.level);
    }
    if (message.expired_str !== "") {
      obj.expired_str = message.expired_str;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OnlineRankV3_OnlineList_UInfo_Guard>, I>>(
    base?: I,
  ): OnlineRankV3_OnlineList_UInfo_Guard {
    return OnlineRankV3_OnlineList_UInfo_Guard.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OnlineRankV3_OnlineList_UInfo_Guard>, I>>(
    object: I,
  ): OnlineRankV3_OnlineList_UInfo_Guard {
    const message = createBaseOnlineRankV3_OnlineList_UInfo_Guard();
    message.level = object.level ?? 0;
    message.expired_str = object.expired_str ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
