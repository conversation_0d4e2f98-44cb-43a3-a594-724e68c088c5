import React from 'react'
import clsx from 'clsx'

import type { RoomNameUpdate } from '@/lib/event-parsers/types/room-name-update'

import type { EventModeProps } from '@/components/event'

export interface RoomNameUpdateProps extends React.HTMLAttributes<HTMLDivElement> {
  data: RoomNameUpdate
  mode: EventModeProps
}

export default function RoomNameUpdateItem({ data, mode, ...rest }: RoomNameUpdateProps) {
  return (
    <div
      className={clsx(
        'event',
        `event--${data.type}`,
        `event-type--${data.type}`,
        `origin--${data.origin}`,
        `origin-index--${data.originIdx}`
      )}
      data-uid={data.uid}
      data-timestamp={data.timestampNormalized}
      {...rest}
    >
      <div className='message'>直播间标题更新：{data.message}</div>
      <div className='message'>
        分区：{data.parentArea} - {data.area}
      </div>
    </div>
  )
}
