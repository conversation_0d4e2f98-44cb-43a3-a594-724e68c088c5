import React from 'react'
import clsx from 'clsx'
import { IconEyeOff } from '@tabler/icons-react'

import type { SuperChat } from '@/lib/event-parsers/types/superchat'
import useGlobalStore from '@/lib/store'

import { getPerkLevel } from '@/utils/eventAssetsUtils'
import { formatDate } from '@/utils/formatDate'
// import { parseLinks } from '@/utils/parseLinks'
import { getGiftRank } from '@/utils/getGiftRank'
import { nf } from '@/utils/numberFormat'
import { timeFromNow } from '@/utils/timeFromNow'

import { useOptions } from '@/hooks/useOptions'

import Avatar from '@/components/chat/avatar'
import Medal from '@/components/chat/medal'
import MessageTranslation from '@/components/chat/message-translation'
import type { AsComponentProps, EventModeProps } from '@/components/event'
import { Tooltip } from '@/components/ui/tooltip'
import UserDropdown from '@/components/UserDropdown'

export interface SuperChatProps extends React.HTMLAttributes<HTMLDivElement> {
  data: SuperChat
  mode: EventModeProps
  as: AsComponentProps
}

/**
 * 醒目留言
 */
export default function SuperChatItem({ data, mode, as, ...rest }: SuperChatProps) {
  const options = useOptions()
  const { uiLang, colorScheme, showMedal, baseFontSize, useCst } = options
  const dashboardLiveGuards = useGlobalStore(state => state.dashboardLiveGuards)

  const price = data.price
  const avatarSize = 320 * (baseFontSize / 20)
  const avatarUrl = `${data.avatar}@${avatarSize}w_${avatarSize}h`
  const showTranslation = data?.transMark === 1 && data?.messageTrans
  const isSpoilerText = data.message.startsWith('||')

  const giftRank = getGiftRank(price)

  return (
    <div
      className={clsx(
        'event',
        `event--${data.type}`,
        `event-type--${data.type}`,
        `event-show-as--${as}`,
        `origin--${data.origin}`,
        `origin-index--${data.originIdx}`,
        `event-superchat-id--${data.scId}`,
        `event-superchat-rank--${giftRank}`,
        `event-price-rank--${giftRank}`,
        data.read && 'read',
        data.deleted && 'deleted',
        showTranslation && 'has-translation',
        colorScheme === 'dark' && 'dark'
      )}
      data-uid={data.uid}
      data-timestamp={data.timestampNormalized}
      {...rest}
      style={
        {
          '--inline-avatar-url': `url(${avatarUrl})`,
        } as React.CSSProperties
      }
      // tabIndex={0}
    >
      <div className={`content`}>
        <div className={`top`}>
          <Avatar
            uid={data.uid}
            avatar={avatarUrl}
            guard={data.guardType}
            perkLevel={getPerkLevel(dashboardLiveGuards, {
              roomId: data.origin,
            })}
          />

          <Avatar
            uid={data.uid}
            avatar={avatarUrl}
            guard={data.guardType}
            perkLevel={getPerkLevel(dashboardLiveGuards, {
              roomId: data.origin,
            })}
            className={`avatar-alt-top hidden`}
          />

          <div className={`meta`}>
            {as !== 'sticky' && (
              <div className={`username-wrap`}>
                {/* Fans medal badge */}
                {(showMedal || mode === 'dashboard') && (
                  <Medal
                    data={data.medal}
                    mode={mode}
                    perkLevel={
                      data?.medal?.resolvedPerkLevel ||
                      getPerkLevel(dashboardLiveGuards, {
                        roomId: data.medal.room,
                      })
                    }
                  />
                )}

                <div className={`username`}>
                  {mode === 'obs' && <div className='username-text line-clamp-1'>{data.username}</div>}

                  {mode === 'dashboard' && (
                    <UserDropdown event={data}>
                      <div className='username-text line-clamp-1 cursor-pointer'>{data.username}</div>
                    </UserDropdown>
                  )}
                </div>
              </div>
            )}

            <div className={`price`}>
              <span className={`price-currency`}>CN¥</span>
              <span className={`price-figure`}>{nf.format(data.priceNormalized)}</span>
            </div>

            {mode === 'dashboard' && (
              <div className={`timestamp-wrap`}>
                {data.deleted && (
                  <Tooltip label={'已被系统或房管删除'}>
                    <IconEyeOff className={`deleted-badge`} />
                  </Tooltip>
                )}
                <Tooltip
                  label={formatDate(new Date(data.timestampNormalized), {
                    locale: uiLang === 'zh-Hans' ? 'zh-CN' : 'en-US',
                    localTime: !useCst,
                    format: {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      weekday: 'short',
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit',
                    },
                  })}
                >
                  <div className={`timestamp`}>
                    {` `}
                    {timeFromNow(data.timestampNormalized, {
                      locale: uiLang === 'zh-Hans' ? 'zh-CN' : 'en-US',
                    })}
                  </div>
                </Tooltip>
              </div>
            )}
          </div>
        </div>

        {as !== 'sticky' && (
          <div className={clsx(isSpoilerText && 'spoiler', 'message')}>
            <div className='message-text'>
              {data.message}

              {mode === 'dashboard' ? (
                <MessageTranslation
                  message={data.message}
                  roomId={data.origin}
                  className={'message-translation'}
                  eventType={data.type}
                  eventId={data.id}
                />
              ) : null}
            </div>
            {showTranslation && <div className={`message-translation`}>🌐 {data.messageTrans}</div>}

            <Avatar
              uid={data.uid}
              avatar={avatarUrl}
              guard={data.guardType}
              perkLevel={getPerkLevel(dashboardLiveGuards, {
                roomId: data.origin,
              })}
              className={`avatar-alt-bottom hidden`}
            />
          </div>
        )}
      </div>
    </div>
  )
}
