@layer components {
  .event--superchat {
    --avatar-size: calc(var(--event-font-size) * 2.5);
    --event-border-radius: calc(var(--1px) * 6);

    margin: calc(var(--event-font-size) * 0.25) 0;
    font-family: var(--event-font-family);
    font-size: calc(var(--event-font-size) * 1.125);
    line-height: var(--event-line-height-base);
    color: var(--white);
    /* NOTE: does not work well on dark templates */
    background-color: var(--event-superchat-bg, var(--bg-color));
    border-radius: var(--event-border-radius);

    &.dark {
      background-color: transparent;
    }

    &.deleted {
      opacity: 0.6;
    }

    &.read {
      opacity: 0.5 !important;
      filter: grayscale(0.8) !important;
    }

    /* Used by sticky bar */
    &.event-show-as--sticky {
      --avatar-size: var(--event-line-height);
      --event-border-radius: calc(var(--1px) * 100);

      font-size: var(--event-font-size);

      .top {
        --fallback-text: var(--event-superchat-message-text, #fff);

        color: var(--fallback-text);
        border-radius: var(--event-border-radius);
        font-weight: bold;
        padding: calc(var(--event-font-size) * 0.25);
        padding-right: calc(var(--event-font-size) * 1);
      }

      &.event-superchat-rank--1 .top {
        color: var(--event-superchat-message-text-1, var(--fallback-text));
        background: var(--event-superchat-bg-1, var(--blue-dark));
      }
      &.event-superchat-rank--2 .top {
        color: var(--event-superchat-message-text-2, var(--fallback-text));
        background: var(--event-superchat-bg-2, var(--green-dark));
      }
      &.event-superchat-rank--3 .top {
        color: var(--event-superchat-message-text-3, var(--fallback-text));
        background: var(--event-superchat-bg-3, var(--amber-darker));
      }
      &.event-superchat-rank--4 .top {
        color: var(--event-superchat-message-text-4, var(--fallback-text));
        background: var(--event-superchat-bg-4, var(--orange-dark));
      }
      &.event-superchat-rank--5 .top {
        color: var(--event-superchat-message-text-5, var(--fallback-text));
        background: var(--event-superchat-bg-5, var(--red-dark));
      }
      &.event-superchat-rank--6 .top {
        color: var(--event-superchat-message-text-6, var(--fallback-text));
        background: var(--event-superchat-bg-6, var(--red-darkest));
      }

      &.dark.event-superchat-rank--1 .top {
        color: var(--event-superchat-message-text-1, var(--fallback-text));
        background: var(--event-superchat-bg-1, var(--blue-50));
      }
      &.dark.event-superchat-rank--2 .top {
        color: var(--event-superchat-message-text-2, var(--fallback-text));
        background: var(--event-superchat-bg-2, var(--green-50));
      }
      &.dark.event-superchat-rank--3 .top {
        color: var(--event-superchat-message-text-3, var(--fallback-text));
        background: var(--event-superchat-bg-3, var(--amber-50));
      }
      &.dark.event-superchat-rank--4 .top {
        color: var(--event-superchat-message-text-4, var(--fallback-text));
        background: var(--event-superchat-bg-4, var(--orange-50));
      }
      &.dark.event-superchat-rank--5 .top {
        color: var(--event-superchat-message-text-5, var(--fallback-text));
        background: var(--event-superchat-bg-5, var(--red-50));
      }
      &.dark.event-superchat-rank--6 .top {
        color: var(--event-superchat-message-text-6, var(--fallback-text));
        background: var(--event-superchat-bg-6, var(--red-60));
      }

      &.dark {
        background-color: transparent;
      }
    }

    &.event-superchat-rank--1 .top {
      color: var(--event-superchat-top-text-1, var(--fallback-text));
      background: var(--event-superchat-top-bg-1, var(--blue-30));
    }
    &.event-superchat-rank--2 .top {
      color: var(--event-superchat-top-text-2, var(--fallback-text));
      background: var(--event-superchat-top-bg-2, var(--green-30));
    }
    &.event-superchat-rank--3 .top {
      color: var(--event-superchat-top-text-3, var(--fallback-text));
      background: var(--event-superchat-top-bg-3, var(--amber-30));
    }
    &.event-superchat-rank--4 .top {
      color: var(--event-superchat-top-text-4, var(--fallback-text));
      background: var(--event-superchat-top-bg-4, var(--orange-30));
    }
    &.event-superchat-rank--5 .top {
      color: var(--event-superchat-top-text-5, var(--fallback-text));
      background: var(--event-superchat-top-bg-5, var(--red-30));
    }
    &.event-superchat-rank--6 .top {
      color: var(--event-superchat-top-text-6, var(--fallback-text));
      background: var(--event-superchat-top-bg-6, var(--red-40));
    }

    &.event-superchat-rank--1 .message {
      color: var(--event-superchat-message-text-1, var(--fallback-text));
      background: var(--event-superchat-bg-1, var(--blue-dark));
    }
    &.event-superchat-rank--2 .message {
      color: var(--event-superchat-message-text-2, var(--fallback-text));
      background: var(--event-superchat-bg-2, var(--green-dark));
    }
    &.event-superchat-rank--3 .message {
      color: var(--event-superchat-message-text-3, var(--fallback-text));
      background: var(--event-superchat-bg-3, var(--amber-darker));
    }
    &.event-superchat-rank--4 .message {
      color: var(--event-superchat-message-text-4, var(--fallback-text));
      background: var(--event-superchat-bg-4, var(--orange-darker));
    }
    &.event-superchat-rank--5 .message {
      color: var(--event-superchat-message-text-5, var(--fallback-text));
      background: var(--event-superchat-bg-5, var(--red-dark));
    }
    &.event-superchat-rank--6 .message {
      color: var(--event-superchat-message-text-6, var(--fallback-text));
      background: var(--event-superchat-bg-6, var(--red-darkest));
    }

    &.dark.event-superchat-rank--1 .message {
      color: var(--event-superchat-message-text-1, var(--fallback-text));
      background: var(--event-superchat-bg-1, var(--blue-50));
    }
    &.dark.event-superchat-rank--2 .message {
      color: var(--event-superchat-message-text-2, var(--fallback-text));
      background: var(--event-superchat-bg-2, var(--green-50));
    }
    &.dark.event-superchat-rank--3 .message {
      color: var(--event-superchat-message-text-3, var(--fallback-text));
      background: var(--event-superchat-bg-3, var(--amber-50));
    }
    &.dark.event-superchat-rank--4 .message {
      color: var(--event-superchat-message-text-4, var(--fallback-text));
      background: var(--event-superchat-bg-4, var(--orange-50));
    }
    &.dark.event-superchat-rank--5 .message {
      color: var(--event-superchat-message-text-5, var(--fallback-text));
      background: var(--event-superchat-bg-5, var(--red-50));
    }
    &.dark.event-superchat-rank--6 .message {
      color: var(--event-superchat-message-text-6, var(--fallback-text));
      background: var(--event-superchat-bg-6, var(--red-60));
    }

    .content {
      display: grid;
    }

    .meta {
      display: flex;
      flex-wrap: wrap;
      gap: 0 calc(var(--event-font-size) * 0.375);
      flex: auto;
    }

    .username-wrap {
      display: inline-flex;
      gap: calc(var(--event-font-size) * 0.375);
      align-items: center;
      flex: auto;
      width: 100%;
    }

    .username {
      display: inline-flex;
      align-items: center;
      gap: 0.25rem;
      font-weight: bold;
      word-break: break-word;

      a {
        color: inherit;
      }
    }

    .top {
      --fallback-text: var(--event-superchat-top-text, var(--text-color));
      display: inline-flex;
      align-items: center;
      gap: calc(var(--event-font-size) * 0.375);
      line-height: var(--event-line-height);
      color: var(--fallback-text);
      padding: calc(var(--event-font-size) * 0.5) calc(var(--event-font-size) * 0.5) calc(var(--event-font-size) * 0.25);
      border-radius: var(--event-border-radius) var(--event-border-radius) 0 0;
    }

    .message {
      --fallback-text: var(--event-superchat-message-text, #fff);
      color: var(--fallback-text);
      padding: calc(var(--event-font-size) * 0.5);
      line-height: var(--event-line-height);
      border-radius: 0 0 var(--event-border-radius) var(--event-border-radius);
      word-break: break-word;

      a {
        color: #fff;
        text-decoration: underline;
      }

      &.spoiler {
        filter: blur(5px);
        transition: filter 0.2s ease-in-out;

        &:hover {
          filter: blur(0);
        }
      }
    }

    .message-translation {
      font-size: var(--event-font-size);
      line-height: calc(var(--event-line-height) * 0.875);
    }

    .price {
      display: flex;
      flex: auto;
    }

    .price-currency {
      opacity: 0.8;
      font-weight: normal;
    }

    .price-figure {
      font-weight: bold;
    }

    .timestamp-wrap {
      display: flex;
      align-items: center;
      gap: 0.25rem;
    }

    .deleted-badge {
      color: var(--red-light);
    }

    .timestamp {
      font-weight: normal;
      font-size: var(--event-font-size);
      opacity: 60%;
    }

    .hidden {
      display: none;
    }
  }
}
