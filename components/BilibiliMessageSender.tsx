import { useRef, useState } from 'react'
import { toast } from 'sonner'
import type { BilibiliInternal } from '@laplace.live/internal'

import { Api } from '@/lib/const'

import { useLocalStorage } from '@/hooks/useLocalStorage'

// import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import Loading from '@/components/ui/loading'

interface BilibiliMessageSenderProps {
  roomId: number
}

export function BilibiliMessageSender({ roomId }: BilibiliMessageSenderProps) {
  const [message, setMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [localStorageOptions] = useLocalStorage()
  const { loginSyncToken } = localStorageOptions
  const inputRef = useRef<HTMLInputElement>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!message.trim()) {
      toast.error('请输入消息内容', { id: 'sender-error-empty' })
      return
    }

    if (!loginSyncToken) {
      toast.error('请先在设置中配置同步登录密钥', { id: 'sender-error-login-sync-token' })
      return
    }

    setIsLoading(true)

    try {
      // clear the input immediately after the request is sent
      setMessage('')

      const url = 'https://workers.vrp.moe/bilibili/live-send'
      // const url = `${Api.OrbitWorkers}/bilibili/live-send`
      // const url = 'http://localhost:8789/bilibili/live-send'
      const resp = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          roomId,
          content: message,
          loginSyncToken,
        }),
      })

      const data: BilibiliInternal.HTTPS.Prod.DanmakuSend = await resp.json()

      if (resp.ok && data.code === 0 && data.message === '') {
        // toast.success('消息发送成功', {
        //   description: message,
        // })
        // re-focus the input
        inputRef.current?.focus()
      } else {
        setMessage(prev => prev + `${message}`)

        let predefinedError = ''

        if (data.code === 0 && data.message === 'f' && data.msg === 'f') {
          predefinedError = '错误 f：弹幕中含全局屏蔽词'
        }

        if (data.code === 0 && data.message === 'k' && data.msg === 'k') {
          predefinedError = '错误 k：弹幕中含直播间屏蔽词'
        }

        toast.error(`「${message}」发送失败`, {
          description: predefinedError || data.message || JSON.stringify(data) || '未知错误',
        })
      }
    } catch (error) {
      setMessage(message)
      console.warn('Error sending message:', error)
      toast.error('发送失败，请检查网络连接', { id: 'sender-error-network' })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <Input
          inputSize={'sm'}
          id='bilibili-message-sender'
          ref={inputRef}
          value={message}
          onChange={e => setMessage(e.target.value)}
          placeholder='输入弹幕…'
          // disabled={isLoading}
          className='w-full border-none inset-shadow-none'
          rightSection={isLoading ? <Loading /> : undefined}
          rightSectionClassName='w-8'
        />
        {/* <Button size={'sm'} type='submit' disabled={isLoading || !message.trim() || !loginSyncToken}>
          {isLoading ? '发送中...' : '发送'}
        </Button> */}
      </form>
    </div>
  )
}
