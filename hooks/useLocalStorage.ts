import superjson from 'superjson'
import useLocalStorageState from 'use-local-storage-state'
import type { ConnectionModes } from '@laplace.live/internal'

import { DEFAULT_NAME, DEFAULT_ROOM, DEFAULT_UID, LOCAL_STORAGE_KEY, MedalLevel, type ColorScheme } from '@/lib/const'

import type { EventModeProps } from '@/components/event'

export interface RoomHistoryItem {
  /**
   * value is room id
   */
  value: string
  uid: string
  /**
   * 由于 Mantine multiselect 组件的限制，定义了 label 后则只能搜索到 label 字段，因此
   * 强制将 label 字段设置为房间号（等于 value）字段，使得房间号可搜索，用户名请单独使用下方
   * 的 username 字段
   */
  label: string
  username: string
  room?: string
}

export interface RoomHistoryItemOpenPlatform {
  /**
   * value is room id
   */
  value: string
  uid: string
  /**
   * 由于 Mantine multiselect 组件的限制，定义了 label 后则只能搜索到 label 字段，因此
   * 强制将 label 字段设置为房间号（等于 value）字段，使得房间号可搜索，用户名请单独使用下方
   * 的 username 字段
   */
  label: string
  /**
   * 开放平台由于多了身份码，因此需要额外保存房间号
   */
  room: string
  username: string
}

export const localStorageOptions = {
  /** bilibili live rooms ID in array format */
  roomIds: ['5440'] as string[],

  /** bilibili live room search history */
  roomSearchHistory: [
    {
      value: DEFAULT_ROOM,
      uid: DEFAULT_UID,
      label: DEFAULT_ROOM,
      username: DEFAULT_NAME,
    },
  ] as RoomHistoryItem[],

  /** bilibili live room search history */
  roomSearchHistoryOpenPlatform: [
    {
      value: DEFAULT_ROOM,
      uid: DEFAULT_UID,
      label: DEFAULT_ROOM,
      username: DEFAULT_NAME,
    },
  ] as RoomHistoryItem[],

  colorScheme: 'light' as ColorScheme,

  /**
   * Running mode
   */
  runningMode: 'obs' as EventModeProps,

  /**
   * WebSocket 连接模式
   */
  connectionMode: 'direct' as ConnectionModes,

  /**
   * 界面语言
   */
  uiLang: 'zh-Hans' as string,

  /**
   * 控制台模式下，隐藏价值低于 ¥n 的礼物
   */
  panelGiftFilterByPrice: 1 as number,

  /**
   * 控制台模式下，隐藏已读项目
   */
  panelEventHideRead: false as boolean,

  /**
   * 控制台模式下，隐藏抽奖结果项目
   */
  panelEventHideResult: false as boolean,

  /**
   * 自定义 CSS 样式
   *
   * @deprecated No longer used since all-in-one object for localstorage options
   * is slow
   */
  customCss: '' as string,

  /**
   * 控制台是否显示直播间基本信息
   */
  dashboardShowMetrics: true as boolean,

  /**
   * 控制台是否强制使用 CST 标准时间
   * @deprecated use `useCst`
   */
  dashboardUseCst: false as boolean,

  /**
   * 控制台是否显示排行榜用户
   */
  dashboardShowTopRankUsers: true as boolean,

  /**
   * 是否强制使用 CST 标准时间
   */
  useCst: false as boolean,

  /**
   * 自定义云端弹幕 API
   */
  danmakuFetcherApi: '' as string,
  eventFetcherAuth: '' as string,

  // OBS options
  showUsername: true as boolean,
  showAvatar: true as boolean,
  showAvatarFrame: true as boolean,
  showSystemMessage: true as boolean,
  showStickyBar: true as boolean,
  /**
   * @deprecated No longer supported
   */
  sortStickyBarByPrice: false as boolean,
  showPhoneNotVerified: false as boolean,
  /**
   * @deprecated No longer supported
   */
  showOnlyCurrentMedal: false as boolean,
  showAutoDanmaku: false as boolean,
  /**
   * @deprecated No longer supported
   */
  showUserLevelAbove: 0 as number,
  showGiftPriceAbove: 1 as number,
  showGiftHighlightAbove: 29.99 as number,
  showGiftStickyAbove: 29.99 as number,
  /**
   * @deprecated No longer supported
   */
  showGiftStickyAlways: false as boolean,
  showGiftFree: false as boolean,
  showEnterEvent: false as boolean,
  showEnterEventCurrentGuardOnly: false as boolean,
  showFollowEvent: false as boolean,
  showDanmaku: true as boolean,
  showGift: true as boolean,
  showSuperChat: true as boolean,
  showToast: true as boolean,
  showRedEnvelop: true as boolean,
  showLottery: true as boolean,
  showNotice: false as boolean,
  showGuardBadge: true as boolean,

  /**
   * @deprecated Use CSS variables instead
   */
  eventMessageColor: '' as string,

  /**
   * @deprecated Use CSS variables instead
   */
  eventGuardUsernameColor0: '' as string,

  /**
   * @deprecated Use CSS variables instead
   */
  eventGuardUsernameColor1: '' as string,

  /**
   * @deprecated Use CSS variables instead
   */
  eventGuardUsernameColor2: '' as string,

  /**
   * @deprecated Use CSS variables instead
   */
  eventGuardUsernameColor3: '' as string,
  showMedal: false as boolean,
  showMedalLightenedOnly: true as boolean,
  showWealthMedal: false as boolean,
  /**
   * @deprecated No longer supported
   */
  showUserLvl: false as boolean,
  /**
   * @deprecated No longer supported
   */
  showEmote: true as boolean,
  showCurrentRank: true as boolean,
  showModBadge: true as boolean,
  limitEventAmount: 50 as number,
  limitStickyAmount: 10 as number,
  /**
   * @deprecated No longer supported
   */
  autoHideEvent: 0 as number,
  /**
   * @deprecated 改用 useState
   */
  filterByMedalRange: [MedalLevel.Min, MedalLevel.Max] as [number, number],
  baseFontSize: 20 as number,
  /**
   * @deprecated 不再使用
   */
  altDanmakuLayout: false as boolean,
  /**
   * @deprecated 不再使用
   */
  customAvatarApi: '' as string,
  /**
   * 显示弹幕特效（OBS）
   */
  showGiftEffect: false as boolean,
  /**
   * 只显示价格高于 ¥n 的礼物特效（OBS）
   */
  showGiftEffectAbove: 99 as number,
  /**
   * 显示弹幕特效（控制台）
   */
  dashboardAutoShowGiftEffect: true as boolean,
  /**
   * 只显示价格高于 ¥n 的礼物特效（控制台）
   */
  dashboardAutoShowGiftEffectAbove: 0 as number,

  remoteEmotesApi: '' as string,
  /**
   * OBS 自定义场景，由 OBS CEF API 传入
   */
  sceneName: '' as string,
  /**
   * 登录状态同步 token
   */
  loginSyncToken: '' as string,
  /**
   * 自定义同步服务器
   */
  loginSyncServer: '' as string,
  /**
   * 是否启用云端主题
   * 初始值定义为 undefined，表示为 optional 状态
   */
  enableCloudTheme: undefined as boolean | undefined,
  /**
   * OBS 用云端主题
   */
  cloudTheme: '' as string,
  /**
   * 远程主题
   */
  remoteTheme: undefined as string | undefined,
}

export type LocalStorageOptionsProps = typeof localStorageOptions
export type LocalStorageOptionsKeys = keyof typeof localStorageOptions

export const useLocalStorage = () =>
  useLocalStorageState(LOCAL_STORAGE_KEY, {
    defaultValue: localStorageOptions,
    serializer: superjson,
  })
