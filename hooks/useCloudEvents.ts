import { useRef } from 'react'
import useSWR, { type Fetcher } from 'swr'

import type { EventFetcherResp } from '@/types'

import { Api } from '@/lib/const'
import type { LaplaceEvent } from '@/lib/event-parsers/types'

import { filterEmptyParams } from '@/utils/filterEmptyParams'

import { useOptions } from '@/hooks/useOptions'

// const FETCH_INTERVAL = 5000 // 5 seconds, for debugging
const FETCH_INTERVAL = 60 * 1000 * 20 // 20 minutes
// const FULL_FETCH_INTERVAL = 60 * 1000 * 60 // 1 hour

function useCloudEvents(roomId: string) {
  const sanitizedQuery = roomId.replace(/[^0-9]/g, '')
  const options = useOptions()
  const { danmakuFetcherApi, eventFetcherAuth } = options

  // Track if we've done the initial full load
  const hasInitialLoadCompleted = useRef(false)

  // Store messages from the initial full load
  const storedMessagesRef = useRef<LaplaceEvent[]>([])

  // Only apply auth key if custom server defined
  const authParam = danmakuFetcherApi ? eventFetcherAuth : undefined

  // Base params that affect the cache key (excluding full parameter)
  const baseCacheParams = {
    auth: authParam,
  }

  const baseCacheUrlParams = filterEmptyParams(baseCacheParams)

  // const defaultApiBase = `http://localhost:8080/events`
  // const defaultApiBase = `https://event-fetcher.laplace.live`
  const defaultApiBase = Api.EventFetcher

  const apiBase = danmakuFetcherApi ? `${danmakuFetcherApi}` : defaultApiBase

  // Use base params for cache key to prevent refetching when fullLoad changes
  const cacheKey = sanitizedQuery ? `${apiBase}/events/${sanitizedQuery}?${baseCacheUrlParams}` : null

  // Custom fetcher that handles full load and message persistence
  const fetcher: Fetcher<EventFetcherResp, string> = async key => {
    // Determine if we should do a full load
    const shouldDoFullLoad = !hasInitialLoadCompleted.current

    // Params for the actual fetch (including full parameter conditionally)
    const fetchParams = {
      full: shouldDoFullLoad ? 1 : undefined,
      auth: authParam,
    }

    const fetchUrlParams = filterEmptyParams(fetchParams)

    // Extract the base URL without query params from the cache key
    const [baseUrl] = key.split('?')
    // Construct the actual fetch URL with full parameter if needed
    const fetchUrl = `${baseUrl}?${fetchUrlParams}`
    const resp = await fetch(fetchUrl)
    if (!resp.ok) {
      throw new Error('An error occurred while fetching cloud events')
    }
    const result: EventFetcherResp = await resp.json()

    // Process the response
    if (result?.status === 200 && result?.data) {
      if (shouldDoFullLoad && result.data.length > 0) {
        // Store messages from initial full load
        const messages = result.data.filter((event: LaplaceEvent) => event.type === 'message')
        storedMessagesRef.current = messages
        hasInitialLoadCompleted.current = true
      } else if (!shouldDoFullLoad) {
        // For subsequent fetches, merge stored messages with new data
        // Filter out any messages from the new data to avoid duplicates
        const nonMessageEvents = result.data.filter((event: LaplaceEvent) => event.type !== 'message')

        // Return a new result with merged data
        return {
          ...result,
          data: [...storedMessagesRef.current, ...nonMessageEvents],
        }
      }
    }

    return result
  }

  const { data, error, isLoading, mutate, isValidating } = useSWR(cacheKey, fetcher, {
    refreshInterval: FETCH_INTERVAL,
  })

  return {
    eventsFromCloud: data,
    isCloudEventsLoading: isLoading,
    isCloudEventsValidating: isValidating,
    isCloudEventsError: error,
    mutate,
  }
}

export default useCloudEvents
